"""
Delivery Bot for Wiz Aroma Delivery System
Provides limited order details to delivery personnel for order acceptance.
Access restricted to authorized delivery personnel Telegram IDs.
"""

import telebot
from telebot import types
import datetime
from typing import Dict, List, Any, Optional
import logging

from src.config import (
    DELIVERY_BOT_TOKEN,
    DELIVERY_BOT_AUTHORIZED_IDS,
    logger
)
from src.data_storage import (
    load_awaiting_receipt,
    load_order_status,
    get_restaurant_by_id,
    get_area_by_id
)
from src.firebase_db import get_data
from src.data_models import (
    awaiting_receipt,
    order_status,
    delivery_personnel_assignments
)
from src.utils.delivery_personnel_utils import (
    get_delivery_assignment_by_order,
    get_delivery_personnel_by_telegram_id,
    update_assignment_status,
    get_assignments_for_personnel
)

# Import order tracking notifications (avoid circular import by importing at runtime)
def get_order_tracking_notifications():
    """Get order tracking notification functions"""
    try:
        from src.bots.order_track_bot import (
            notify_delivery_accepted,
            notify_delivery_completed
        )
        return notify_delivery_accepted, notify_delivery_completed
    except ImportError:
        logger.warning("Could not import order tracking notifications")
        return None, None

# Initialize the delivery bot
delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to access the delivery bot"""
    return user_id in DELIVERY_BOT_AUTHORIZED_IDS

def access_denied_message(message):
    """Send access denied message to unauthorized users"""
    delivery_bot.reply_to(
        message,
        "🚫 Access Denied\n\nYou are not authorized to use this delivery system."
    )

def get_personnel_by_telegram_id(telegram_id: int):
    """Get delivery personnel by Telegram ID with fresh data from Firebase"""
    try:
        # Load fresh data directly from Firebase
        personnel_data = get_data("delivery_personnel") or {}

        for personnel_id, personnel_dict in personnel_data.items():
            if personnel_dict.get('telegram_id') == str(telegram_id):
                # Convert dict back to DeliveryPersonnel object using from_dict method
                from src.data_models import DeliveryPersonnel
                personnel = DeliveryPersonnel.from_dict(personnel_dict)
                return personnel

        return None

    except Exception as e:
        logger.error(f"Error getting personnel by Telegram ID {telegram_id}: {e}")
        return None

@delivery_bot.message_handler(commands=['start'])
def start_command(message):
    """Handle /start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    # Check if user is registered as delivery personnel
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel. Please contact admin."
        )
        return
    
    welcome_text = f"""
🚚 **Delivery System**
Welcome {personnel.name}!

**Available Commands:**
📋 /orders - View available orders
✅ /accept [order_number] - Accept an order
❌ /decline [order_number] - Decline an order
📦 /myorders - View your assigned orders
🔄 /status [order_number] [status] - Update order status
📍 /location - Update your location

**Order Statuses:**
• `assigned` - Order assigned to you
• `accepted` - You accepted the order
• `picked_up` - Order picked up from restaurant
• `delivered` - Order delivered to customer
• `cancelled` - Order cancelled
    """
    
    delivery_bot.reply_to(message, welcome_text, parse_mode='Markdown')

@delivery_bot.message_handler(commands=['orders'])
def view_available_orders_command(message):
    """View available orders for delivery"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Load confirmed orders from Firebase
        confirmed_orders = get_data("confirmed_orders") or {}

        if not confirmed_orders:
            delivery_bot.reply_to(
                message,
                "📭 No orders available for delivery."
            )
            return
        
        # Filter orders that are assigned to this personnel or unassigned
        available_orders = []
        
        for order_number, order_data in confirmed_orders.items():
            assignment = get_delivery_assignment_by_order(order_number)
            
            # Show unassigned orders or orders assigned to this personnel
            if not assignment or assignment.get('personnel_id') == personnel.personnel_id:
                # Get restaurant info
                restaurant_id = order_data.get('restaurant_id')
                restaurant = get_restaurant_by_id(restaurant_id)
                restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
                
                # Format order items for delivery personnel (limited info)
                items_text = ""
                if order_data.get('items'):
                    for item in order_data['items']:
                        items_text += f"• {item.get('name', 'Unknown')} (x{item.get('quantity', 1)}) - {item.get('price', 0)} birr\n"
                
                # Create the delivery-specific format as requested
                order_info = f"""
📋 **Order #{order_number}**
📱 Phone: {order_data.get('phone_number', 'N/A')}
🏪 Restaurant: {restaurant_name}
📍 Delivery to: {order_data.get('delivery_location', 'N/A')}

📋 ORDER ITEMS:
{items_text.strip()}

💰 Subtotal: {order_data.get('subtotal', 0)} birr
                """
                
                available_orders.append((order_number, order_info.strip()))
        
        if not available_orders:
            delivery_bot.reply_to(
                message,
                "📭 No orders available for you at the moment."
            )
            return
        
        # Send orders with accept/decline buttons
        for order_number, order_info in available_orders[:5]:  # Limit to 5 orders
            # Create inline keyboard for accept/decline
            markup = types.InlineKeyboardMarkup()
            accept_btn = types.InlineKeyboardButton("✅ Accept", callback_data=f"accept_{order_number}")
            decline_btn = types.InlineKeyboardButton("❌ Decline", callback_data=f"decline_{order_number}")
            markup.row(accept_btn, decline_btn)
            
            delivery_bot.send_message(
                message.chat.id,
                order_info,
                parse_mode='Markdown',
                reply_markup=markup
            )
                
    except Exception as e:
        logger.error(f"Error in view_available_orders_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error retrieving orders. Please try again later."
        )

@delivery_bot.callback_query_handler(func=lambda call: call.data.startswith(('accept_order_', 'decline_order_', 'accept_', 'decline_', 'complete_order_')))
def handle_order_decision(call):
    """Handle order accept/decline decisions for both broadcast and assigned orders"""
    user_id = call.from_user.id

    if not is_authorized(user_id):
        delivery_bot.answer_callback_query(call.id, "Access denied")
        return

    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.answer_callback_query(call.id, "Not registered as delivery personnel")
        return

    try:
        # Handle both new broadcast format (accept_order_/decline_order_) and old format (accept_/decline_)
        if call.data.startswith('accept_order_'):
            action = "accept"
            order_number = call.data.replace('accept_order_', '')
        elif call.data.startswith('decline_order_'):
            action = "decline"
            order_number = call.data.replace('decline_order_', '')
        elif call.data.startswith('complete_order_'):
            action = "complete"
            order_number = call.data.replace('complete_order_', '')
        else:
            # Old format
            action, order_number = call.data.split('_', 1)

        if action == "accept":
            # For broadcast orders, we need to assign first, then accept
            from src.utils.delivery_personnel_utils import assign_order_to_personnel
            from src.firebase_db import get_data, set_data

            # Check if order is still available (not already assigned)
            confirmed_orders = get_data("confirmed_orders") or {}
            if order_number not in confirmed_orders:
                delivery_bot.answer_callback_query(call.id, "❌ Order no longer available")
                return

            order_data = confirmed_orders[order_number]
            if order_data.get('delivery_status') != 'pending_assignment':
                delivery_bot.answer_callback_query(call.id, "❌ Order already assigned to another delivery person")
                return

            # Check if personnel has capacity
            from src.utils.delivery_personnel_utils import get_real_time_capacity
            current_capacity = get_real_time_capacity(personnel.personnel_id)
            if current_capacity >= personnel.max_capacity:
                delivery_bot.answer_callback_query(call.id, f"❌ You are at maximum capacity ({current_capacity}/{personnel.max_capacity})")
                return

            # Assign the order to this personnel
            delivery_fee = order_data.get('delivery_fee', 0)
            assignment_id = assign_order_to_personnel(order_number, personnel.personnel_id, delivery_fee)

            if assignment_id:
                # Update order status in Firebase
                order_data['delivery_status'] = 'assigned'
                order_data['assigned_to'] = personnel.personnel_id
                order_data['assigned_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                set_data(f"confirmed_orders/{order_number}", order_data)

                delivery_bot.answer_callback_query(call.id, "✅ Order accepted!")

                # Create "Complete Order" button for the accepted order
                complete_markup = types.InlineKeyboardMarkup()
                complete_btn = types.InlineKeyboardButton(
                    "🏁 Complete Order",
                    callback_data=f"complete_order_{order_number}"
                )
                complete_markup.add(complete_btn)

                # Update message with Complete Order button
                try:
                    logger.info(f"🔄 Updating message for order {order_number} with Complete Order button")

                    # Create a simplified message to avoid formatting issues
                    new_message_text = f"✅ Order #{order_number} ACCEPTED\n\nOrder assigned to you\nClick 'Complete Order' when delivery is finished"

                    delivery_bot.edit_message_text(
                        new_message_text,
                        call.message.chat.id,
                        call.message.message_id,
                        reply_markup=complete_markup
                    )

                    logger.info(f"✅ Successfully updated message with Complete Order button for order {order_number}")

                except Exception as edit_error:
                    logger.error(f"❌ Failed to update message with Complete Order button: {edit_error}")
                    logger.error(f"   Error type: {type(edit_error).__name__}")
                    logger.error(f"   Error details: {str(edit_error)}")

                    # Try sending a new message instead of editing
                    try:
                        logger.info(f"🔄 Attempting to send new message with Complete Order button for order {order_number}")
                        delivery_bot.send_message(
                            call.message.chat.id,
                            f"✅ Order #{order_number} ACCEPTED\n\nOrder assigned to you\nClick 'Complete Order' when delivery is finished",
                            reply_markup=complete_markup
                        )
                        logger.info(f"✅ Sent new message with Complete Order button for order {order_number}")
                    except Exception as send_error:
                        logger.error(f"❌ Failed to send new message: {send_error}")
                        logger.error(f"   Send error type: {type(send_error).__name__}")
                        logger.error(f"   Send error details: {str(send_error)}")

                        # Last resort: just send a simple notification
                        try:
                            delivery_bot.send_message(
                                call.message.chat.id,
                                f"Order #{order_number} accepted! Please complete delivery and notify when finished."
                            )
                        except:
                            logger.error(f"❌ Complete failure to send any message for order {order_number}")

                # Notify order tracking bot with delivery person details
                try:
                    notify_delivery_accepted, _ = get_order_tracking_notifications()
                    if notify_delivery_accepted:
                        notify_delivery_accepted(order_number, personnel.name)
                except Exception as e:
                    logger.error(f"Failed to send acceptance notification: {e}")

                # Notify other delivery personnel that order is no longer available
                try:
                    from src.utils.delivery_personnel_utils import delivery_personnel
                    from src.config import DELIVERY_BOT_TOKEN
                    import telebot

                    notify_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)

                    for other_personnel_id, other_personnel_data in delivery_personnel.items():
                        if (other_personnel_id != personnel.personnel_id and
                            other_personnel_data.get('telegram_id')):
                            try:
                                notify_bot.send_message(
                                    other_personnel_data['telegram_id'],
                                    f"📋 Order #{order_number} has been accepted by {personnel.name}",
                                    parse_mode='Markdown'
                                )
                            except Exception as notify_error:
                                logger.error(f"Failed to notify personnel {other_personnel_id}: {notify_error}")

                except Exception as notify_others_error:
                    logger.error(f"Failed to notify other personnel: {notify_others_error}")

            else:
                delivery_bot.answer_callback_query(call.id, "❌ Failed to accept order - may have been taken by another delivery person")

        elif action == "decline":
            # Simply decline - no assignment needed
            delivery_bot.answer_callback_query(call.id, "❌ Order declined")
            delivery_bot.edit_message_text(
                f"❌ **Order #{order_number} DECLINED**\n\n{call.message.text}",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )

        elif action == "complete":
            # Handle delivery completion
            from src.firebase_db import get_data, set_data

            # Verify this personnel is assigned to this order
            confirmed_orders = get_data("confirmed_orders") or {}
            if order_number not in confirmed_orders:
                delivery_bot.answer_callback_query(call.id, "❌ Order not found")
                return

            order_data = confirmed_orders[order_number]
            if order_data.get('assigned_to') != personnel.personnel_id:
                delivery_bot.answer_callback_query(call.id, "❌ You are not assigned to this order")
                return

            if order_data.get('delivery_status') == 'completed':
                delivery_bot.answer_callback_query(call.id, "❌ Order already marked as completed")
                return

            # Update order status to completed
            order_data['delivery_status'] = 'completed'
            order_data['completed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            order_data['completed_by'] = personnel.personnel_id
            set_data(f"confirmed_orders/{order_number}", order_data)

            # Update delivery assignment status
            from src.utils.delivery_personnel_utils import update_assignment_status
            update_assignment_status(order_number, personnel.personnel_id, "delivered")

            delivery_bot.answer_callback_query(call.id, "✅ Order marked as completed!")
            delivery_bot.edit_message_text(
                f"✅ **Order #{order_number} COMPLETED**\n\n{call.message.text}\n\n📋 **Status**: Delivery completed\n⏰ **Completed at**: {order_data['completed_at']}\n\n🔔 Customer will now receive confirmation request.",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )

            # Notify order tracking bot about delivery completion
            try:
                _, notify_delivery_completed = get_order_tracking_notifications()
                if notify_delivery_completed:
                    notify_delivery_completed(order_number, personnel.name)
                    logger.info(f"Sent delivery completion notification for order {order_number}")
            except Exception as e:
                logger.error(f"Failed to send delivery completion notification: {e}")

    except Exception as e:
        logger.error(f"Error in handle_order_decision: {e}")
        delivery_bot.answer_callback_query(call.id, "❌ Error processing request")

@delivery_bot.message_handler(commands=['myorders'])
def view_my_orders_command(message):
    """View orders assigned to this delivery personnel"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Get assignments for this personnel
        assignments = get_assignments_for_personnel(personnel.personnel_id)
        
        if not assignments:
            delivery_bot.reply_to(
                message,
                "📭 You have no assigned orders."
            )
            return
        
        # Load order data
        awaiting_orders = load_awaiting_receipt()
        
        my_orders = []
        for assignment in assignments:
            order_number = assignment.get('order_number')
            if order_number in awaiting_orders:
                order_data = awaiting_orders[order_number]
                
                # Get restaurant info
                restaurant_id = order_data.get('restaurant_id')
                restaurant = get_restaurant_by_id(restaurant_id)
                restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
                
                order_info = f"""
📋 **Order #{order_number}**
📱 Phone: {order_data.get('phone_number', 'N/A')}
🏪 Restaurant: {restaurant_name}
📍 Delivery to: {order_data.get('delivery_location', 'N/A')}
💰 Subtotal: {order_data.get('subtotal', 0)} birr
📊 Status: {assignment.get('status', 'Unknown').title()}
⏰ Assigned: {assignment.get('assigned_at', 'N/A')}
                """
                my_orders.append(order_info.strip())
        
        if my_orders:
            response = "🚚 **Your Assigned Orders:**\n\n" + "\n\n".join(my_orders)
            delivery_bot.reply_to(message, response, parse_mode='Markdown')
        else:
            delivery_bot.reply_to(
                message,
                "📭 You have no current orders."
            )
                
    except Exception as e:
        logger.error(f"Error in view_my_orders_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error retrieving your orders. Please try again later."
        )

@delivery_bot.message_handler(commands=['status'])
def update_order_status_command(message):
    """Update order status"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Parse command: /status ORDER_NUMBER STATUS
        command_parts = message.text.split()
        if len(command_parts) < 3:
            delivery_bot.reply_to(
                message,
                "❌ Usage: /status ORDER_NUMBER STATUS\n\nValid statuses: accepted, picked_up, delivered, cancelled"
            )
            return
        
        order_number = command_parts[1]
        new_status = command_parts[2].lower()
        
        valid_statuses = ['accepted', 'picked_up', 'delivered', 'cancelled']
        if new_status not in valid_statuses:
            delivery_bot.reply_to(
                message,
                f"❌ Invalid status. Valid statuses: {', '.join(valid_statuses)}"
            )
            return
        
        # Update the status
        success = update_assignment_status(order_number, personnel.personnel_id, new_status)
        if success:
            delivery_bot.reply_to(
                message,
                f"✅ Order #{order_number} status updated to: {new_status.title()}"
            )

            # Notify order tracking bot if order is marked as delivered
            if new_status == "delivered":
                try:
                    _, notify_delivery_completed = get_order_tracking_notifications()
                    if notify_delivery_completed:
                        notify_delivery_completed(order_number, personnel.name)
                except Exception as e:
                    logger.error(f"Failed to send delivery completion notification: {e}")

        else:
            delivery_bot.reply_to(
                message,
                f"❌ Failed to update order #{order_number} status. Order may not be assigned to you."
            )
                
    except Exception as e:
        logger.error(f"Error in update_order_status_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error updating order status. Please try again later."
        )

@delivery_bot.message_handler(func=lambda message: True)
def handle_unknown_command(message):
    """Handle unknown commands"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    help_text = """
❓ **Unknown Command**

**Available Commands:**
📋 /orders - View available orders
✅ /accept [order_number] - Accept an order
❌ /decline [order_number] - Decline an order
📦 /myorders - View your assigned orders
🔄 /status [order_number] [status] - Update order status

Example: `/status ORD_20241230_001 picked_up`
    """
    
    delivery_bot.reply_to(message, help_text, parse_mode='Markdown')

def run_delivery_bot():
    """Run the delivery bot"""
    logger.info("Starting Delivery Bot...")
    try:
        delivery_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        logger.error(f"Delivery Bot error: {e}")
        raise

if __name__ == "__main__":
    run_delivery_bot()
