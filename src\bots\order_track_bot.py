"""
Order Tracking Bot for Wiz Aroma Delivery System
Tracks financially confirmed orders and their delivery status.
Access restricted to authorized Telegram IDs.
"""

import telebot
from telebot import types
import datetime
from typing import Dict, List, Any, Optional
import logging

from src.config import (
    ORDER_TRACK_BOT_TOKEN,
    ORDER_TRACK_BOT_AUTHORIZED_IDS,
    logger
)
from src.data_storage import (
    load_pending_admin_reviews,
    load_awaiting_receipt,
    load_order_status,
    load_order_history,
    get_restaurant_by_id,
    get_area_by_id
)
from src.firebase_db import get_data, set_data, delete_data
from src.data_models import (
    pending_admin_reviews,
    awaiting_receipt,
    order_status,
    user_order_history,
    delivery_personnel_assignments
)
from src.utils.delivery_personnel_utils import get_delivery_assignment_by_order

# Initialize the order tracking bot
order_track_bot = telebot.TeleBot(ORDER_TRACK_BOT_TOKEN)

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to access the order tracking bot"""
    return user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS

def access_denied_message(message):
    """Send access denied message to unauthorized users"""
    order_track_bot.reply_to(
        message,
        "🚫 Access Denied\n\nYou are not authorized to use this order tracking system."
    )

@order_track_bot.message_handler(commands=['start'])
def start_command(message):
    """Handle /start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    welcome_text = """
🔍 **Order Tracking System**
Welcome to the Wiz Aroma Order Tracking Bot!

**Available Commands:**
📋 /orders - View all financially confirmed orders
🔍 /track [order_number] - Track specific order
📊 /status - View order status summary
📈 /stats - View delivery statistics
🔄 /refresh - Refresh order data

This system tracks orders that have been financially confirmed and shows their delivery status.
    """
    
    order_track_bot.reply_to(message, welcome_text, parse_mode='Markdown')

@order_track_bot.message_handler(commands=['orders'])
def view_orders_command(message):
    """View all financially confirmed orders"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Load confirmed orders from Firebase
        confirmed_orders = get_data("confirmed_orders") or {}

        if not confirmed_orders:
            order_track_bot.reply_to(
                message,
                "📭 No financially confirmed orders found."
            )
            return
        
        # Create order list
        order_list = []
        for order_number, order_data in confirmed_orders.items():
            # Get order status
            status = order_data.get('status', 'CONFIRMED')
            
            # Get restaurant info
            restaurant_id = order_data.get('restaurant_id')
            restaurant = get_restaurant_by_id(restaurant_id)
            restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
            
            # Get delivery assignment status
            assignment = get_delivery_assignment_by_order(order_number)
            delivery_status = "Not Assigned"
            if assignment:
                delivery_status = assignment.get('status', 'Unknown').title()
            
            order_info = f"""
📋 **Order #{order_number}**
🏪 Restaurant: {restaurant_name}
📱 Phone: {order_data.get('phone_number', 'N/A')}
📍 Delivery: {order_data.get('delivery_location', 'N/A')}
💰 Total: {order_data.get('subtotal', 0)} birr
📊 Status: {status}
🚚 Delivery: {delivery_status}
⏰ Created: {order_data.get('created_at', 'N/A')}
            """
            order_list.append(order_info.strip())
        
        # Split into chunks if too long
        if len(order_list) <= 5:
            response = "🔍 **Financially Confirmed Orders:**\n\n" + "\n\n".join(order_list)
            order_track_bot.reply_to(message, response, parse_mode='Markdown')
        else:
            # Send in chunks
            for i in range(0, len(order_list), 5):
                chunk = order_list[i:i+5]
                chunk_text = f"🔍 **Orders ({i+1}-{min(i+5, len(order_list))} of {len(order_list)}):**\n\n" + "\n\n".join(chunk)
                order_track_bot.send_message(message.chat.id, chunk_text, parse_mode='Markdown')
                
    except Exception as e:
        logger.error(f"Error in view_orders_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error retrieving orders. Please try again later."
        )

@order_track_bot.message_handler(commands=['track'])
def track_order_command(message):
    """Track a specific order by order number"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Extract order number from command
        command_parts = message.text.split()
        if len(command_parts) < 2:
            order_track_bot.reply_to(
                message,
                "❌ Please provide an order number.\nUsage: /track ORDER_NUMBER"
            )
            return
        
        order_number = command_parts[1]
        
        # Load order data from confirmed orders collection
        confirmed_orders = get_data("confirmed_orders") or {}

        if order_number not in confirmed_orders:
            order_track_bot.reply_to(
                message,
                f"❌ Order #{order_number} not found in confirmed orders."
            )
            return

        order_data = confirmed_orders[order_number]
        user_id_str = str(order_data.get('user_id', ''))
        status = order_statuses.get(user_id_str, 'Unknown')
        
        # Get restaurant info
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
        
        # Get delivery assignment details
        assignment = get_delivery_assignment_by_order(order_number)
        delivery_info = "🚚 **Delivery Status:** Not Assigned"
        
        if assignment:
            from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
            personnel = get_delivery_personnel_by_id(assignment.get('personnel_id'))
            personnel_name = personnel.name if personnel else "Unknown Driver"
            
            delivery_info = f"""
🚚 **Delivery Status:** {assignment.get('status', 'Unknown').title()}
👤 **Driver:** {personnel_name}
📞 **Driver Phone:** {personnel.phone_number if personnel else 'N/A'}
⏰ **Assigned:** {assignment.get('assigned_at', 'N/A')}
🕐 **Est. Pickup:** {assignment.get('estimated_pickup_time', 'N/A')}
🕑 **Est. Delivery:** {assignment.get('estimated_delivery_time', 'N/A')}
            """
            
            if assignment.get('actual_pickup_time'):
                delivery_info += f"\n✅ **Picked Up:** {assignment.get('actual_pickup_time')}"
            if assignment.get('actual_delivery_time'):
                delivery_info += f"\n🎯 **Delivered:** {assignment.get('actual_delivery_time')}"
        
        # Format order items
        items_text = ""
        if order_data.get('items'):
            items_text = "\n📋 **ORDER ITEMS:**\n"
            for item in order_data['items']:
                items_text += f"• {item.get('name', 'Unknown')} (x{item.get('quantity', 1)}) - {item.get('price', 0)} birr\n"
        
        detailed_info = f"""
🔍 **Order Tracking - #{order_number}**

📱 **Phone:** {order_data.get('phone_number', 'N/A')}
🏪 **Restaurant:** {restaurant_name}
📍 **Delivery to:** {order_data.get('delivery_location', 'N/A')}
{items_text}
💰 **Subtotal:** {order_data.get('subtotal', 0)} birr
📊 **Order Status:** {status}
⏰ **Created:** {order_data.get('created_at', 'N/A')}

{delivery_info}
        """
        
        order_track_bot.reply_to(message, detailed_info.strip(), parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Error in track_order_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error tracking order. Please try again later."
        )

@order_track_bot.message_handler(commands=['status'])
def status_summary_command(message):
    """View order status summary"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Load data
        awaiting_orders = load_awaiting_receipt()
        order_statuses = load_order_status()
        
        # Count orders by status
        status_counts = {}
        delivery_status_counts = {}
        
        for order_number, order_data in awaiting_orders.items():
            user_id_str = str(order_data.get('user_id', ''))
            status = order_statuses.get(user_id_str, 'Unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # Count delivery statuses
            assignment = get_delivery_assignment_by_order(order_number)
            if assignment:
                delivery_status = assignment.get('status', 'unknown')
                delivery_status_counts[delivery_status] = delivery_status_counts.get(delivery_status, 0) + 1
            else:
                delivery_status_counts['not_assigned'] = delivery_status_counts.get('not_assigned', 0) + 1
        
        summary = f"""
📊 **Order Status Summary**

**Total Financially Confirmed Orders:** {len(awaiting_orders)}

**Order Statuses:**
"""
        
        for status, count in status_counts.items():
            summary += f"• {status}: {count}\n"
        
        summary += "\n**Delivery Statuses:**\n"
        for delivery_status, count in delivery_status_counts.items():
            summary += f"• {delivery_status.replace('_', ' ').title()}: {count}\n"
        
        summary += f"\n⏰ **Last Updated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        order_track_bot.reply_to(message, summary, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Error in status_summary_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error generating status summary. Please try again later."
        )

@order_track_bot.message_handler(commands=['refresh'])
def refresh_data_command(message):
    """Refresh order data from Firebase"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Reload data from Firebase
        load_awaiting_receipt()
        load_order_status()
        load_pending_admin_reviews()
        
        order_track_bot.reply_to(
            message,
            "✅ Order data refreshed successfully from Firebase."
        )
        
    except Exception as e:
        logger.error(f"Error in refresh_data_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error refreshing data. Please try again later."
        )

@order_track_bot.message_handler(func=lambda message: True)
def handle_unknown_command(message):
    """Handle unknown commands"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    help_text = """
❓ **Unknown Command**

**Available Commands:**
📋 /orders - View all financially confirmed orders
🔍 /track [order_number] - Track specific order
📊 /status - View order status summary
🔄 /refresh - Refresh order data

Example: `/track ORD_20241230_001`
    """
    
    order_track_bot.reply_to(message, help_text, parse_mode='Markdown')

def send_order_status_update(order_number: str, new_status: str, additional_info: str = "", replace_previous: bool = False):
    """Send order status update to all authorized tracking users"""
    try:
        # Get order data
        confirmed_orders = get_data("confirmed_orders") or {}
        if order_number not in confirmed_orders:
            logger.warning(f"Order {order_number} not found for status update")
            return

        order_data = confirmed_orders[order_number]

        # Get restaurant info
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"

        # Create status update message with comprehensive order details
        status_message = f"""
🔔 **Order Status Update**

📋 **Order #{order_number}**
🏪 **Restaurant**: {restaurant_name}
📱 **Customer**: {order_data.get('phone_number', 'N/A')}
📍 **Delivery**: {order_data.get('delivery_location', 'N/A')}
💰 **Subtotal**: {order_data.get('subtotal', 0)} Birr

🚚 **Status**: {new_status}
⏰ **Updated**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        if additional_info:
            status_message += f"\n📝 **Details**: {additional_info}"

        # Add order items if available
        if 'items' in order_data:
            status_message += "\n\n📋 **Order Items**:"
            for item in order_data['items']:
                item_name = item.get('name', 'Unknown Item')
                item_price = item.get('price', 0)
                item_quantity = item.get('quantity', 1)
                status_message += f"\n• {item_name} x{item_quantity} - {item_price} birr"

        # Handle message replacement for status updates
        if replace_previous:
            # Try to update previous message if message_id is stored
            previous_message_id = order_data.get(f'tracking_message_id')
            if previous_message_id:
                for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
                    try:
                        order_track_bot.edit_message_text(
                            status_message,
                            user_id,
                            previous_message_id,
                            parse_mode='Markdown'
                        )
                        logger.info(f"Updated status message for order {order_number} to user {user_id}")
                    except Exception as e:
                        logger.warning(f"Failed to edit message for user {user_id}, sending new message: {e}")
                        # Send new message if edit fails
                        try:
                            sent_message = order_track_bot.send_message(user_id, status_message, parse_mode='Markdown')
                            # Store new message ID
                            order_data[f'tracking_message_id'] = sent_message.message_id
                            set_data(f"confirmed_orders/{order_number}", order_data)
                        except Exception as send_e:
                            logger.error(f"Failed to send new status update to user {user_id}: {send_e}")
            else:
                # No previous message ID, send new messages
                for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
                    try:
                        sent_message = order_track_bot.send_message(user_id, status_message, parse_mode='Markdown')
                        # Store message ID for future updates
                        order_data[f'tracking_message_id'] = sent_message.message_id
                        set_data(f"confirmed_orders/{order_number}", order_data)
                        logger.info(f"Sent status update for order {order_number} to user {user_id}")
                    except Exception as e:
                        logger.error(f"Failed to send status update to user {user_id}: {e}")
        else:
            # Send new messages without replacement
            for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
                try:
                    sent_message = order_track_bot.send_message(user_id, status_message, parse_mode='Markdown')
                    logger.info(f"Sent status update for order {order_number} to user {user_id}")
                except Exception as e:
                    logger.error(f"Failed to send status update to user {user_id}: {e}")

    except Exception as e:
        logger.error(f"Error sending order status update: {e}")

def notify_delivery_assignment(order_number: str, personnel_name: str, personnel_phone: str):
    """Notify when order is assigned to delivery personnel"""
    send_order_status_update(
        order_number,
        "Assigned to Delivery Personnel",
        f"Driver: {personnel_name} ({personnel_phone})",
        replace_previous=False  # Initial assignment, send new message
    )

def notify_delivery_accepted(order_number: str, personnel_name: str):
    """Notify when delivery personnel accepts the order"""
    send_order_status_update(
        order_number,
        "Accepted by Delivery Personnel",
        f"Driver {personnel_name} has accepted the order",
        replace_previous=True  # Replace the assignment message
    )

def notify_delivery_completed(order_number: str, personnel_name: str):
    """Notify when delivery personnel marks order as completed"""
    send_order_status_update(
        order_number,
        "Delivery Completed",
        f"Driver {personnel_name} has marked the order as delivered. Customer confirmation request sent.",
        replace_previous=True  # Replace the previous status message
    )

    # Send confirmation button to customer
    send_customer_confirmation_request(order_number)

def send_customer_confirmation_request(order_number: str):
    """Send confirmation request to customer when order is marked as delivered"""
    try:
        logger.info(f"🔔 Starting customer confirmation request for order {order_number}")

        # Get order data
        confirmed_orders = get_data("confirmed_orders") or {}
        if order_number not in confirmed_orders:
            logger.warning(f"Order {order_number} not found for customer confirmation")
            return

        order_data = confirmed_orders[order_number]
        customer_user_id = order_data.get('user_id')

        if not customer_user_id:
            logger.warning(f"No customer user ID found for order {order_number}")
            return

        logger.info(f"📱 Sending confirmation request to customer {customer_user_id}")

        # Get restaurant info
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"

        # Import user bot to send message
        from src.bot_instance import bot
        from telebot import types

        # Create confirmation message
        confirmation_message = f"""
🚚 **Delivery Completed!**

📋 **Order #{order_number}**
🏪 **Restaurant**: {restaurant_name}
💰 **Total**: {order_data.get('subtotal', 0)} Birr

Your order has been delivered by our driver.
Please confirm that you have received your order.
"""

        # Create inline keyboard with confirmation button
        markup = types.InlineKeyboardMarkup()
        confirm_btn = types.InlineKeyboardButton(
            "✅ Confirm Received",
            callback_data=f"confirm_delivery_{order_number}"
        )
        markup.add(confirm_btn)

        # Send confirmation request to customer
        logger.info(f"📤 Attempting to send confirmation message to user {customer_user_id}")
        bot.send_message(
            customer_user_id,
            confirmation_message,
            parse_mode='Markdown',
            reply_markup=markup
        )

        logger.info(f"✅ Successfully sent delivery confirmation request for order {order_number} to user {customer_user_id}")

    except Exception as e:
        logger.error(f"❌ Error sending customer confirmation request for order {order_number}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

def cleanup_completed_order(order_number: str):
    """Clean up order cache and send email notification for completed orders"""
    try:
        # Get order data before cleanup
        confirmed_orders = get_data("confirmed_orders") or {}
        if order_number not in confirmed_orders:
            logger.warning(f"Order {order_number} not found for cleanup")
            return

        order_data = confirmed_orders[order_number]

        # Send email notification for completed order
        try:
            from src.utils.helpers import send_email_notification, format_order_summary
            from src.firebase_db import get_user_names, get_user_phone_numbers

            # Get user info for email
            user_id = order_data.get('user_id')
            user_names = get_user_names()
            user_phones = get_user_phone_numbers()

            user_info = {
                'name': user_names.get(str(user_id), 'Unknown'),
                'phone': user_phones.get(str(user_id), order_data.get('phone_number', 'N/A'))
            }

            # Format email content
            email_subject = f"Order Completed Successfully - #{order_number}"
            email_body = format_order_summary(order_data, user_info)
            email_body += f"\n\n✅ <strong>Order Status:</strong> Successfully Delivered and Confirmed by Customer"
            email_body += f"\n📅 <strong>Completed At:</strong> {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # Send email notification
            email_sent = send_email_notification(
                "<EMAIL>",
                email_subject,
                email_body
            )

            if email_sent:
                logger.info(f"Completion email sent for order {order_number}")
            else:
                logger.error(f"Failed to send completion email for order {order_number}")

        except Exception as email_error:
            logger.error(f"Error sending completion email for order {order_number}: {email_error}")

        # Clean up order cache from storage
        try:
            # Move order to completed orders collection
            completed_order_data = order_data.copy()
            completed_order_data['completed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            completed_order_data['final_status'] = 'CUSTOMER_CONFIRMED'

            # Store in completed orders
            set_data(f"completed_orders/{order_number}", completed_order_data)

            # Remove from confirmed orders (active cache)
            delete_data(f"confirmed_orders/{order_number}")

            # Clean up any related cache data
            from src.data_storage import clean_up_order_data
            if user_id:
                clean_up_order_data(int(user_id), order_number)

            logger.info(f"Successfully cleaned up order cache for order {order_number}")

        except Exception as cleanup_error:
            logger.error(f"Error cleaning up order cache for order {order_number}: {cleanup_error}")

    except Exception as e:
        logger.error(f"Error in cleanup_completed_order for order {order_number}: {e}")

def notify_customer_confirmed(order_number: str):
    """Notify when customer confirms order completion"""
    send_order_status_update(
        order_number,
        "Order Fully Completed",
        "Customer has confirmed receipt of the order. Order process complete.",
        replace_previous=True  # Replace the delivery completed message
    )

    # Clean up order cache and send email notification for completed order
    cleanup_completed_order(order_number)

def run_order_track_bot():
    """Run the order tracking bot"""
    logger.info("Starting Order Tracking Bot...")
    try:
        order_track_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        logger.error(f"Order Tracking Bot error: {e}")
        raise

if __name__ == "__main__":
    run_order_track_bot()
