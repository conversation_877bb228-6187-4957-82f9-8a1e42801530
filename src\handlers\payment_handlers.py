"""
Payment handlers for the Wiz Aroma Delivery Bot.
Contains handlers for payment processing.
"""

import datetime
import requests
from functools import wraps
import time
import telebot
from telebot import types

from src.bot_instance import bot, finance_bot, notification_bot
from src.utils.handler_registration import register_handler
from src.config import (
    logger,
    FINANCE_CHAT_ID,
    NOTIFICATION_CHAT_ID,
    TELEBIRR_PHONE,
    TELEBIRR_NAME,
    CBE_ACCOUNT_NUMBER,
    CBE_ACCOUNT_NAME,
    BOA_ACCOUNT_NUMBER,
    BOA_ACCOUNT_NAME,
    SUPPORT_PHONE_1,
    SUPPORT_PHONE_2,
    SUPPORT_TELEGRAM,
)
from src.data_models import (
    orders,
    order_status,
    user_points,
    awaiting_receipt,
    current_order_numbers,
    pending_admin_reviews,
    admin_remarks,
)
from src.data_storage import (
    orders,
    order_status,
    awaiting_receipt,
    user_points,
    update_points_balance,
    clean_up_order_data,
    get_points_balance,
    save_points,
    load_points,
    get_restaurant_by_id,
)
from src.utils.keyboards import (
    get_points_confirmation_markup,
    get_payment_receipt_markup,
    get_payment_method_markup,
    get_main_menu_markup,
    get_finance_action_markup,
)
from src.utils.validation import calculate_points


def retry_on_timeout(max_retries=3, delay=1):
    """Decorator to retry a function on timeout - only used for receipt processing"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except (
                    TimeoutError,
                    requests.exceptions.Timeout,
                    requests.exceptions.ConnectionError,
                ) as e:
                    retries += 1
                    if retries == max_retries:
                        raise e
                    time.sleep(delay)
            return func(*args, **kwargs)

        return wrapper

    return decorator


def handle_timeout_error(chat_id):
    """Handle timeout errors gracefully"""
    try:
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("🔄 Back to Payment Options"),
            types.KeyboardButton("❌ Cancel Order"),
        )
        bot.send_message(
            chat_id,
            "⚠️ Connection timeout. Please try again or contact support if the issue persists.\n\n"
            f"📞 Need help? Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            reply_markup=markup,
        )
    except Exception as e:
        logger.error(f"Error in handle_timeout_error: {e}")


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_PAYMENT_METHOD"
    and message.text in ["📱 Telebirr", "🏦 CBE Bank", "🏦 BOA Bank", "✅ Use Points"]
)
def handle_payment_choice(message):
    """Handle payment method selection"""
    user_id = message.from_user.id
    current_status = order_status.get(user_id)
    logger.info(
        f"PAYMENT SELECTION: User {user_id} selected '{message.text}' (status: {current_status})"
    )

    # Get order
    order = orders.get(user_id)
    if not order:
        logger.error(
            f"No active order found for user {user_id} when selecting payment method"
        )
        bot.send_message(message.chat.id, "❌ No active order found.")
        return

    # Log to see exact order contents
    logger.info(f"Processing payment choice for order: {order}")

    # Get order details
    subtotal = order.get("subtotal", 0)
    delivery_fee = order.get("delivery_fee", 0)
    total_amount = subtotal + delivery_fee

    # Use get_points_balance instead of directly accessing the in-memory dictionary
    user_point_balance = get_points_balance(user_id)

    # Make sure any pending points changes are saved before checking balance
    save_points(
        user_points
    )  # Ensure points are written to disk before checking balance

    # Log values for debugging
    logger.info(
        f"Order details - Subtotal: {subtotal}, Delivery: {delivery_fee}, Total: {total_amount}"
    )

    # Handle points usage
    if message.text == "✅ Use Points":
        if user_point_balance >= delivery_fee:
            # Ask for confirmation
            markup = get_points_confirmation_markup()
            remaining_amount = subtotal
            message_text = (
                "💫 Points Usage Confirmation\n\n"
                f"You are about to use {delivery_fee} points to cover the delivery fee.\n\n"
                "Points Summary:\n"
                f"• Current Balance: {user_point_balance} points\n"
                f"• Points to Use: {delivery_fee} points\n"
                f"• Remaining Balance: {user_point_balance - delivery_fee} points\n\n"
                f"Payment After Points:\n"
                f"• Order Total: {remaining_amount} birr\n\n"
                "Please confirm if you want to use your points."
            )

            bot.send_message(message.chat.id, message_text, reply_markup=markup)
            order_status[user_id] = "AWAITING_POINTS_CONFIRMATION"
            logger.info(f"User {user_id} is now awaiting points confirmation")
            return
        else:
            bot.reply_to(
                message,
                "❌ Insufficient points balance. Please select another payment method.\n\n"
                f"📞 Need help? Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            )
            # Show payment options again
            markup = get_payment_method_markup(False)  # No points option
            bot.send_message(
                message.chat.id, "Please select a payment method:", reply_markup=markup
            )
            order_status[user_id] = "AWAITING_PAYMENT_METHOD"
            return

    # Handle Telebirr Payment
    if message.text == "📱 Telebirr":
        logger.info(f"Processing Telebirr payment for user {user_id}")

        # Set payment method
        payment_method = "Telebirr"
        order["payment_method"] = payment_method

        # Calculate amount
        points_used = order.get("points_used", 0)
        payment_amount = subtotal if points_used > 0 else total_amount

        # Show payment instructions
        markup = get_payment_receipt_markup()
        payment_text = (
            "📱 *TELEBIRR MOBILE MONEY TRANSFER DETAILS*\n\n"
            f"💰 *Amount to Pay:* {payment_amount} birr\n\n"
            "📱 *Account Details:*\n"
            "• Mobile Money: Telebirr\n"
            f"• Phone Number: {TELEBIRR_PHONE}\n"
            f"• Account Name: {TELEBIRR_NAME}\n\n"
            "📸 *After Payment:*\n"
            "• Take a screenshot of your payment confirmation\n"
            "• Send ONLY a PHOTO/SCREENSHOT (not document or other file type)\n"
            "• Make sure the image is compressed and clear\n\n"
            "Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep!"
        )

        # Send payment instructions
        try:
            bot.send_message(
                message.chat.id,
                payment_text,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            logger.info(
                f"Successfully sent Telebirr payment instructions to user {user_id}"
            )

            # Update order status
            order_status[user_id] = "AWAITING_RECEIPT"

            # Store receipt info
            awaiting_receipt[user_id] = {
                "amount": payment_amount,
                "method": payment_method,
                "order_number": order.get("order_number"),
            }

            logger.info(
                f"Successfully processed Telebirr payment selection for user {user_id}"
            )
        except Exception as e:
            logger.error(
                f"Error sending Telebirr payment instructions to user {user_id}: {e}"
            )
            bot.send_message(
                message.chat.id,
                "❌ Error processing payment. Please try again or contact support.\n\n"
                f"📞 Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            )
        return

    # Handle CBE Bank Payment
    if message.text == "🏦 CBE Bank":
        logger.info(f"Processing CBE Bank payment for user {user_id}")

        # Set payment method
        payment_method = "CBE Bank"
        order["payment_method"] = payment_method

        # Calculate amount
        points_used = order.get("points_used", 0)
        payment_amount = subtotal if points_used > 0 else total_amount

        # Show payment instructions
        markup = get_payment_receipt_markup()
        payment_text = (
            "🏦 *CBE BANK TRANSFER DETAILS*\n\n"
            f"💰 *Amount to Pay:* {payment_amount} birr\n\n"
            "🏦 *Account Details:*\n"
            "• Bank: Commercial Bank of Ethiopia (CBE)\n"
            f"• Account Number: {CBE_ACCOUNT_NUMBER}\n"
            f"• Account Name: {CBE_ACCOUNT_NAME}\n\n"
            "📸 *After Payment:*\n"
            "• Take a screenshot of your payment confirmation\n"
            "• Send ONLY a PHOTO/SCREENSHOT (not document or other file type)\n"
            "• Make sure the image is compressed and clear\n\n"
            "Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep!"
        )

        # Send payment instructions
        try:
            bot.send_message(
                message.chat.id,
                payment_text,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            logger.info(f"Successfully sent CBE payment instructions to user {user_id}")

            # Update order status
            order_status[user_id] = "AWAITING_RECEIPT"

            # Store receipt info
            awaiting_receipt[user_id] = {
                "amount": payment_amount,
                "method": payment_method,
                "order_number": order.get("order_number"),
            }

            logger.info(
                f"Successfully processed CBE Bank payment selection for user {user_id}"
            )
        except Exception as e:
            logger.error(
                f"Error sending CBE payment instructions to user {user_id}: {e}"
            )
            bot.send_message(
                message.chat.id,
                "❌ Error processing payment. Please try again or contact support.\n\n"
                f"📞 Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            )
        return

    # Handle BOA Bank Payment
    if message.text == "🏦 BOA Bank":
        logger.info(f"Processing BOA Bank payment for user {user_id}")

        # Set payment method
        payment_method = "BOA Bank"
        order["payment_method"] = payment_method

        # Calculate amount
        points_used = order.get("points_used", 0)
        payment_amount = subtotal if points_used > 0 else total_amount

        # Show payment instructions
        markup = get_payment_receipt_markup()
        payment_text = (
            "🏦 *BOA BANK TRANSFER DETAILS*\n\n"
            f"💰 *Amount to Pay:* {payment_amount} birr\n\n"
            "🏦 *Account Details:*\n"
            "• Bank: Bank of Abissinya (BOA)\n"
            f"• Account Number: {BOA_ACCOUNT_NUMBER}\n"
            f"• Account Name: {BOA_ACCOUNT_NAME}\n\n"
            "📸 *After Payment:*\n"
            "• Take a screenshot of your payment confirmation\n"
            "• Send ONLY a PHOTO/SCREENSHOT (not document or other file type)\n"
            "• Make sure the image is compressed and clear\n\n"
            "Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep!"
        )

        # Send payment instructions
        try:
            bot.send_message(
                message.chat.id,
                payment_text,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            logger.info(f"Successfully sent BOA payment instructions to user {user_id}")

            # Update order status
            order_status[user_id] = "AWAITING_RECEIPT"

            # Store receipt info
            awaiting_receipt[user_id] = {
                "amount": payment_amount,
                "method": payment_method,
                "order_number": order.get("order_number"),
            }

            logger.info(
                f"Successfully processed BOA Bank payment selection for user {user_id}"
            )
        except Exception as e:
            logger.error(
                f"Error sending BOA payment instructions to user {user_id}: {e}"
            )
            bot.send_message(
                message.chat.id,
                "❌ Error processing payment. Please try again or contact support.\n\n"
                f"📞 Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            )
        return


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_POINTS_CONFIRMATION"
    and message.text in ["✅ Confirm Use Points", "❌ Cancel Points Use"]
)
def handle_points_confirmation(message):
    """Handle points usage confirmation"""
    try:
        user_id = message.from_user.id
        order = orders.get(user_id)

        if not order:
            bot.send_message(message.chat.id, "No active order found.")
            return

        if message.text == "✅ Confirm Use Points":
            delivery_fee = order.get("delivery_fee", 0)

            # Ensure we have the latest points data from disk
            user_points = load_points()
            user_point_balance = get_points_balance(user_id)

            # Final verification of points balance
            if user_point_balance >= delivery_fee:
                # Deduct points only after confirmation
                update_points_balance(user_id, -delivery_fee)
                new_point_balance = user_point_balance - delivery_fee

                # Update order to mark points usage
                order["points_used"] = delivery_fee
                order["payment_method"] = "Points+Regular"
                order["points_before"] = user_point_balance
                order["points_after"] = new_point_balance

                # Show bank options for remaining amount
                markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
                markup.add(
                    types.KeyboardButton("📱 Telebirr"),
                    types.KeyboardButton("🏦 CBE Bank"),
                )
                markup.add(types.KeyboardButton("🏦 BOA Bank"))
                markup.add(types.KeyboardButton("❌ Cancel Order"))

                message_text = (
                    "✅ Points successfully used!\n\n"
                    "Points Summary:\n"
                    f"• Points Used: {delivery_fee} points\n"
                    f"• New Balance: {new_point_balance} points\n\n"
                    "Please pay the remaining amount:\n"
                    f"• Order Total: {order.get('subtotal', 0)} birr\n\n"
                    "Select your bank:"
                )

                bot.send_message(message.chat.id, message_text, reply_markup=markup)
                order_status[user_id] = "AWAITING_PAYMENT_METHOD"
            else:
                # Points are no longer sufficient
                markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
                markup.add(
                    types.KeyboardButton("📱 Telebirr"),
                    types.KeyboardButton("🏦 CBE Bank"),
                )
                markup.add(types.KeyboardButton("🏦 BOA Bank"))
                markup.add(types.KeyboardButton("❌ Cancel Order"))

                message_text = (
                    "❌ Points balance has changed and is now insufficient.\n"
                    "Please proceed with regular payment:"
                )

                bot.send_message(message.chat.id, message_text, reply_markup=markup)
                order_status[user_id] = "AWAITING_PAYMENT_METHOD"

        else:  # Cancel points use
            # Show regular payment options
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("📱 Telebirr"),
                types.KeyboardButton("🏦 CBE Bank"),
            )
            markup.add(types.KeyboardButton("🏦 BOA Bank"))
            markup.add(types.KeyboardButton("❌ Cancel Order"))

            # Ensure no points are used
            order["points_used"] = 0
            order["payment_method"] = "Regular"

            # Calculate total amount
            subtotal = order.get("subtotal", 0)
            delivery_fee = order.get("delivery_fee", 0)
            total_amount = subtotal + delivery_fee

            message_text = (
                "Points usage cancelled.\n\n"
                f"Order Summary:\n"
                f"• Items Total: {subtotal} birr\n"
                f"• Delivery Fee: {delivery_fee} birr\n"
                f"• Total Amount: {total_amount} birr\n\n"
                "Please select your bank:"
            )

            bot.send_message(message.chat.id, message_text, reply_markup=markup)
            order_status[user_id] = "AWAITING_PAYMENT_METHOD"

    except Exception as e:
        logger.error(f"Error in handle_points_confirmation: {e}")
        bot.send_message(
            message.chat.id,
            "❌ Error processing points confirmation. Please try again or contact support.\n\n"
            f"📞 Need help? Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
        )


@bot.message_handler(func=lambda message: message.text == "🔄 Back to Bank Options")
def retry_payment_instructions(message):
    """Handle retry of payment instructions"""
    try:
        # Reset status to payment method selection
        user_id = message.from_user.id
        if user_id in order_status:
            order_status[user_id] = "AWAITING_PAYMENT_METHOD"
        if user_id in awaiting_receipt:
            del awaiting_receipt[user_id]

        # Show payment options again
        order = orders.get(user_id)
        if order:
            # Ensure we have the latest points data
            user_points = load_points()
            user_point_balance = get_points_balance(user_id)
            delivery_fee = order.get("delivery_fee", 0)
            markup = get_payment_method_markup(user_point_balance >= delivery_fee)

            bot.send_message(
                message.chat.id,
                "Please select your bank:",
                reply_markup=markup,
            )
        else:
            bot.send_message(
                message.chat.id,
                "❌ Your order was not found. Please start a new order.",
                reply_markup=get_main_menu_markup(),
            )

    except Exception as e:
        logger.error(f"Error in retry_payment_instructions: {e}")
        bot.send_message(
            message.chat.id,
            "❌ Error retrying payment. Please try again or contact support.\n\n"
            f"📞 Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
        )


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id) == "AWAITING_RECEIPT",
    content_types=[
        "text",
        "document",
        "audio",
        "sticker",
        "video",
        "video_note",
        "voice",
        "location",
        "contact",
    ],
)
def handle_non_photo_receipt(message):
    """Handle non-photo receipt submissions"""
    try:
        user_id = message.from_user.id
        # Check if user has an active order and is at the correct step
        if order_status.get(user_id) != "AWAITING_RECEIPT":
            bot.reply_to(
                message,
                "❌ Please complete your order first before sending a payment receipt.",
            )
            return

        order = orders.get(user_id)
        if not order:
            bot.reply_to(message, "❌ No active order found.")
            return

        if not awaiting_receipt.get(user_id):
            bot.reply_to(
                message,
                "❌ Please select a payment method first before sending your receipt.",
            )
            return

        # Handle Cancel Order button
        if message.text == "❌ Cancel Order":
            # Get order number for log
            order_number = order.get("order_number", "unknown")

            # Clean up order data
            clean_up_order_data(user_id, order_number)

            # Return to main menu with Back to Main Menu button
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
                types.KeyboardButton("💫 My Points"),
                types.KeyboardButton("ℹ️ Help"),
            )

            bot.send_message(
                message.chat.id,
                "🚫 Order canceled during payment process.\n"
                "No payment will be processed and your cart has been cleared.",
                reply_markup=markup,
            )

            # Send additional confirmation that they can start over
            bot.send_message(
                message.chat.id,
                "You can start a new order whenever you're ready!",
            )
            return

        # Inform user that only photos are accepted
        bot.reply_to(
            message,
            "❌ Please send a screenshot or photo of your payment receipt. Other file types are not supported.\n"
            "⚠️ Important: Send only compressed images to ensure they can be processed correctly.\n\n"
            f"📞 Need help? Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
        )

    except Exception as e:
        logger.error(f"Error in handle_non_photo_receipt for user {user_id}: {str(e)}")
        bot.reply_to(
            message,
            "❌ An error occurred while processing your request. Please try again.\n\n"
            f"📞 Contact support: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
        )


@bot.message_handler(content_types=["photo"])
def handle_receipt(message):
    """Handle receipt photo submission"""
    try:
        user_id = message.from_user.id

        # Check if user has an active order and is at the correct step
        if order_status.get(user_id) != "AWAITING_RECEIPT":
            bot.reply_to(
                message,
                "❌ Please complete your order first before sending a payment receipt.\n"
                "If you're trying to place an order, please use the '🍽️ Order Food' button.",
            )
            return

        order = orders.get(user_id)
        if not order:
            bot.reply_to(message, "❌ No active order found.")
            return

        # Check if awaiting_receipt exists for this user and is a dictionary
        receipt_info = awaiting_receipt.get(user_id)
        if not receipt_info or not isinstance(receipt_info, dict):
            bot.reply_to(
                message,
                "❌ Please select a payment method first before sending your receipt.",
            )
            return

        # Use existing order number if available, otherwise generate a new one
        order_number = order.get("order_number") or current_order_numbers.get(user_id)
        if not order_number:
            from src.utils.time_utils import generate_order_number

            order_number = generate_order_number(user_id)

        order["order_number"] = order_number
        current_order_numbers[user_id] = order_number  # Store for reference

        # Calculate total amount based on payment type
        subtotal = order["subtotal"]
        delivery_fee = order["delivery_fee"]
        points_used = order.get("points_used", 0)

        # If points were used, only show subtotal in total
        if points_used > 0:
            total_amount = subtotal
            payment_note = f"\nDelivery fee ({delivery_fee} birr) covered by points"
        else:
            total_amount = subtotal + delivery_fee
            payment_note = ""

        # Get payment method
        payment_method = order.get("payment_method", "Not specified")

        # Check if message has a photo
        if not message.photo or len(message.photo) == 0:
            bot.reply_to(
                message,
                "❌ Please send a screenshot or photo of your payment receipt. Other file types are not supported.\n"
                "⚠️ Important: Send only compressed images to ensure they can be processed correctly.",
            )
            return

        # Forward receipt to finance team with buttons - with retry logic
        photo = message.photo[-1]

        @retry_on_timeout(max_retries=3, delay=2)
        def download_and_forward_receipt():
            # Set timeout globally for this operation
            old_timeout = telebot.apihelper.READ_TIMEOUT
            telebot.apihelper.READ_TIMEOUT = 30
            try:
                file_info = bot.get_file(photo.file_id)
                downloaded_file = bot.download_file(file_info.file_path)

                # Create inline keyboard for finance team actions
                markup = get_finance_action_markup(user_id, order_number)

                # Get Telegram username if available
                telegram_username = order.get("username", "")
                if not telegram_username or telegram_username == "No username":
                    telegram_username = order.get("telegram_username", "Not provided")

                # Prepare the caption
                receipt_caption = (
                    f"🧾 New payment receipt received\n"
                    f"📝 Order ID: #{order_number}\n"
                    f"💰 Amount to verify: {total_amount} birr{payment_note}\n"
                    f"💳 Payment Method: {payment_method}\n"
                    f"👤 Customer Name: {order.get('delivery_name', 'N/A')}\n"
                    f"📞 Phone: {order.get('phone_number', 'N/A')}\n"
                    f"🔤 Telegram: @{telegram_username}\n"
                    f"🚪 Delivery Gate: {order.get('delivery_gate', 'N/A')}"
                )

                try:
                    # Forward to finance team
                    finance_bot.send_photo(
                        FINANCE_CHAT_ID,
                        downloaded_file,
                        caption=receipt_caption,
                        reply_markup=markup,
                    )
                    logger.info(
                        f"Successfully forwarded receipt to finance team for order #{order_number}"
                    )
                    return True
                except telebot.apihelper.ApiTelegramException as api_error:
                    if "chat not found" in str(api_error).lower():
                        logger.error(
                            f"Finance chat not found. Make sure the finance admin (ID: {FINANCE_CHAT_ID}) has started a chat with the finance bot."
                        )
                        logger.error(
                            f"IMPORTANT: The user with ID {FINANCE_CHAT_ID} must start a chat with the finance bot @{finance_bot.get_me().username}"
                        )
                        # Save the receipt locally and continue with the order
                        logger.info(f"Saving receipt locally for order #{order_number}")
                        # We'll still return True so the user gets a confirmation
                        return True
                    else:
                        # Re-raise other API exceptions
                        logger.error(
                            f"API Exception when forwarding receipt: {api_error}"
                        )
                        raise
            finally:
                # Restore original timeout
                telebot.apihelper.READ_TIMEOUT = old_timeout

        # Try to process the receipt with retry logic
        if download_and_forward_receipt():
            # Acknowledge receipt to user
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(types.KeyboardButton("🔙 Back to Main Menu"))

            bot.reply_to(
                message,
                "✅ Payment receipt received and forwarded for verification.\n"
                "You will be notified once your payment is verified.\n\n"
                "📞 Need help? Contact us:  0963630623, 0963637233 or @wiz_aroma_contact_center",
                reply_markup=markup,
            )

            # Update order status
            order_status[user_id] = "AWAITING_VERIFICATION"
            awaiting_receipt[user_id] = False
        else:
            raise Exception("Failed to process receipt after retries")

    except (
        TimeoutError,
        requests.exceptions.Timeout,
        requests.exceptions.ConnectionError,
    ) as e:
        logger.error(f"Timeout error handling receipt: {e}")
        bot.reply_to(
            message,
            "⚠️ Network timeout occurred. Please try sending your receipt again in a few moments.\n\n"
            "📞 Need assistance? Contact us:  0963630623, 0963637233 or @wiz_aroma_contact_center",
        )
    except Exception as e:
        logger.error(f"Error handling receipt: {e}")
        bot.reply_to(
            message,
            "❌ Error processing receipt. Please try again or contact our support team.\n\n"
            "📞 Contact us:  0963630623, 0963637233 or @wiz_aroma_contact_center",
        )


def send_order_notification(order_data):
    """Send order notification to the notification channel when approved by finance"""
    try:
        # Extract order details - using correct field names from the order structure
        order_number = order_data.get("order_number", "Unknown")
        phone_number = order_data.get("phone_number", "Unknown")
        restaurant = order_data.get(
            "restaurant", "Unknown"
        )  # Changed from restaurant_name to restaurant
        delivery_location = order_data.get("delivery_gate", "Unknown")
        order_items = order_data.get("items", [])  # Changed from cart_items to items
        special_instructions = order_data.get(
            "order_description", ""
        )  # Changed from special_instructions to order_description

        # Log details for debugging
        logger.info(
            f"Notification for order {order_number} - Restaurant: {restaurant}, Items: {len(order_items)}"
        )

        # Escape Markdown special characters to avoid parsing issues
        if restaurant and isinstance(restaurant, str):
            restaurant = (
                restaurant.replace("-", "\\-").replace(".", "\\.").replace("_", "\\_")
            )

        # Format order items
        items_text = ""
        if order_items and isinstance(order_items, list):
            for item in order_items:
                if isinstance(item, dict):
                    item_name = item.get("name", "Unknown item")
                    # Escape Markdown special characters in item names
                    item_name = (
                        item_name.replace("-", "\\-")
                        .replace(".", "\\.")
                        .replace("_", "\\_")
                    )
                    item_quantity = item.get("quantity", 1)
                    item_price = item.get("price", 0)
                    items_text += (
                        f"• {item_name} (x{item_quantity}) - {item_price} birr\n"
                    )

        # Create the message in the required format
        message_text = f"{order_number}\n\n"
        message_text += f"📱 Phone: {phone_number}\n\n"
        message_text += f"🏪 Restaurant: {restaurant}\n"
        message_text += f"📍 Delivery to: {delivery_location}\n\n\n"
        message_text += f"📋 ORDER ITEMS:\n{items_text}\n"

        # Add special instructions if any
        if special_instructions:
            # Escape Markdown special characters in special instructions
            special_instructions = (
                special_instructions.replace("-", "\\-")
                .replace(".", "\\.")
                .replace("_", "\\_")
            )
            message_text += f"📝 Special Instructions:\n{special_instructions}\n"

        # Log the constructed message for debugging
        logger.debug(
            f"Notification message constructed for order {order_number}: {message_text[:100]}..."
        )

        # Send notification without markdown parsing to avoid issues
        notification_bot.send_message(NOTIFICATION_CHAT_ID, message_text)
        logger.info(f"Order notification sent for order #{order_number}")
        return True
    except Exception as e:
        logger.error(f"Error sending order notification: {e}")
        return False


@register_handler("finance", handler_type="callback_query", func=lambda call: call.data.startswith("verify_"))
def handle_finance_verification(call):
    """Handle verification of payment receipts by finance team"""
    try:
        # Acknowledge the callback query to prevent the loading indicator
        finance_bot.answer_callback_query(call.id)

        # Handle cases where the bot is not set up properly
        if not hasattr(finance_bot, "send_message"):
            logger.error("Finance bot not properly configured")
            return

        # Extract order information
        data = call.data.split(
            "_", 3
        )  # Split into ["verify", "action", "user_id", "order_number"]
        if len(data) < 4:
            logger.error(f"Invalid callback data format: {call.data}")
            return

        action = data[1]  # "approve" or "reject"
        user_id = int(data[2])
        order_number = data[3]  # Keep the full order number as is

        # First try to get the order information from the in-memory orders dictionary
        order = orders.get(user_id)

        # If not found in memory, try to get it from Firebase
        if not order:
            try:
                # Try to fetch the order directly from Firebase
                from src.firebase_db import get_current_orders

                firebase_orders = get_current_orders(str(user_id))
                if (
                    firebase_orders
                    and str(user_id) in firebase_orders
                    and firebase_orders[str(user_id)]
                ):
                    order = firebase_orders[str(user_id)]
                    # Update the in-memory orders dictionary
                    orders[user_id] = order
                    logger.info(f"Retrieved order for user {user_id} from Firebase")
            except Exception as e:
                logger.error(f"Error retrieving order from Firebase: {e}")

        # If not found in Firebase current_orders, check pending_admin_reviews
        if not order:
            try:
                from src.firebase_db import get_pending_admin_reviews

                admin_reviews = get_pending_admin_reviews(order_number)
                if admin_reviews and order_number in admin_reviews:
                    # Use the data from pending_admin_reviews
                    order_data = admin_reviews[order_number]
                    # Create a minimal order object with required fields
                    order = {
                        "status": "PENDING",
                        "order_number": order_number,
                        "delivery_gate": order_data.get("delivery_gate", "Unknown"),
                        "phone_number": order_data.get("phone_number", "Unknown"),
                        "delivery_name": order_data.get("delivery_name", "Customer"),
                        "delivery_fee": order_data.get("delivery_fee", 0),
                        "points_used": order_data.get("points_used", 0),
                    }
                    # Store it in the in-memory dictionary for future reference
                    orders[user_id] = order
                    logger.info(
                        f"Retrieved order {order_number} from pending_admin_reviews"
                    )
            except Exception as e:
                logger.error(f"Error retrieving order from pending_admin_reviews: {e}")

        # If still not found, report error
        if not order:
            finance_bot.send_message(
                call.message.chat.id, f"❌ Error: Order #{order_number} not found."
            )
            return

        # Check if order has already been approved or rejected
        if order.get("status") in ["APPROVED", "REJECTED"]:
            finance_bot.send_message(
                call.message.chat.id,
                f"❌ Order #{order_number} has already been {order.get('status').lower()}.",
            )
            return

        if action == "approve":  # Approve
            try:
                # Update order status
                order["status"] = "APPROVED"
                order["approved_at"] = datetime.datetime.now().strftime(
                    "%Y-%m-%d %H:%M:%S"
                )

                # Ensure all required fields are present with fallback values
                if "delivery_fee" not in order:
                    # Try to get delivery fee from order number or use a default
                    logger.warning(
                        f"Missing delivery_fee in order {order_number}, using default value"
                    )
                    order["delivery_fee"] = 30  # Default delivery fee

                if "delivery_gate" not in order:
                    order["delivery_gate"] = "Unknown"

                if "phone_number" not in order:
                    order["phone_number"] = "Unknown"

                # Prepare order summary for email
                user_info = {
                    "order_number": order.get(
                        "order_number", order_number
                    ),  # Use stored order_number if available
                    "full_name": order.get("delivery_name", "Customer"),
                }

                # Calculate points earned if not using points for payment
                points_message = ""
                try:
                    if not order.get("points_used", 0):
                        # Ensure delivery_fee exists and is valid
                        delivery_fee = order.get(
                            "delivery_fee", 30
                        )  # Default to 30 if missing
                        if not isinstance(delivery_fee, (int, float)):
                            delivery_fee = 30  # Use default if not a number

                        points_earned = calculate_points(delivery_fee)
                        # Update points using update_points_balance function
                        update_points_balance(user_id, points_earned)
                        # Explicitly save points to disk to ensure they're available immediately
                        save_points(user_points)
                        points_message = (
                            f"\n\n💫 You earned {points_earned} points for this order!"
                        )
                        logger.info(
                            f"Added {points_earned} points to user {user_id} for order #{order_number}"
                        )
                except Exception as points_error:
                    logger.error(
                        f"Error processing points for order {order_number}: {points_error}"
                    )
                    points_message = "\n\n💫 Points will be added to your account soon!"

                # Get safe values with defaults for all fields
                display_order_number = user_info.get("order_number", order_number)
                delivery_gate = order.get("delivery_gate", "your delivery location")
                phone_number = order.get("phone_number", "your registered number")

                # Create the markup for reply keyboard
                markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
                markup.add(
                    types.KeyboardButton("🍽️ Order Food"),
                    types.KeyboardButton("🔙 Back to Main Menu"),
                    types.KeyboardButton("💫 My Points"),
                    types.KeyboardButton("ℹ️ Help"),
                )

                # Send confirmation to user with retry logic
                def send_user_approval_notification(retry_count=0, max_retries=2):
                    try:
                        bot.send_message(
                            user_id,
                            f"✅ Awesome! Your payment for Order #{display_order_number} has been verified! 🎉\n\n"
                            f"🚚 Your delicious order is on its way to {delivery_gate}\n"
                            f"📞 Our delivery hero will contact you on {phone_number}\n"
                            f"{points_message}\n\n"
                            f"Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep! 😋",
                            reply_markup=markup,
                        )
                        logger.info(
                            f"Sent approval confirmation to user {user_id} for order #{display_order_number}"
                        )
                        return True
                    except Exception as e:
                        logger.error(
                            f"Error sending approval notification to user {user_id}: {e}"
                        )

                        # Retry if we haven't reached max retries
                        if retry_count < max_retries:
                            logger.info(
                                f"Retrying user notification ({retry_count+1}/{max_retries})..."
                            )
                            time.sleep(1)  # Wait a second before retrying
                            return send_user_approval_notification(
                                retry_count + 1, max_retries
                            )
                        else:
                            logger.error(
                                f"Failed to send approval notification after {max_retries} retries"
                            )
                            return False

                # Try to send notification to user
                notification_sent = send_user_approval_notification()

                # Send notification to the orders channel
                try:
                    send_order_notification(order)
                except Exception as notif_error:
                    logger.error(f"Error sending order notification: {notif_error}")

                # Note: Email notifications for completed orders are now sent only after customer confirmation
                # This is handled in the order tracking bot's cleanup_completed_order function
                logger.info(f"Order #{display_order_number} confirmed - email will be sent after customer confirms delivery")

                # Update finance team message
                try:
                    # Get the timestamp in a safe way
                    approved_at = order.get(
                        "approved_at",
                        datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    )

                    # Check if the message has media (photo)
                    try:
                        # Try to edit caption first (for messages with photos)
                        finance_bot.edit_message_caption(
                            chat_id=call.message.chat.id,
                            message_id=call.message.message_id,
                            caption=f"✅ Payment for Order #{display_order_number} has been approved and processed.\n"
                            f"Time: {approved_at}",
                        )
                        logger.info(
                            f"Updated finance message caption for order #{display_order_number}"
                        )
                    except Exception as caption_error:
                        if (
                            "there is no caption in the message to edit"
                            in str(caption_error).lower()
                        ):
                            # Try to edit text instead for messages without photos
                            try:
                                finance_bot.edit_message_text(
                                    chat_id=call.message.chat.id,
                                    message_id=call.message.message_id,
                                    text=f"✅ Payment for Order #{display_order_number} has been approved and processed.\n"
                                    f"Time: {approved_at}",
                                )
                                logger.info(
                                    f"Updated finance message text for order #{display_order_number}"
                                )
                            except Exception as text_error:
                                logger.error(
                                    f"Could not update finance message text: {text_error}"
                                )
                                # Fall back to sending a new message
                                send_approval_confirmation(
                                    display_order_number, approved_at
                                )
                        else:
                            logger.error(
                                f"Could not update finance message caption: {caption_error}"
                            )
                            # Fall back to sending a new message
                            send_approval_confirmation(
                                display_order_number, approved_at
                            )
                except Exception as e:
                    logger.error(f"Could not update finance message: {e}")
                    # If editing fails, try to send a new message instead
                    send_approval_confirmation(display_order_number, approved_at)

                # Helper function to send a new approval message
                def send_approval_confirmation(order_num, timestamp):
                    try:
                        finance_bot.send_message(
                            call.message.chat.id,
                            f"✅ Payment for Order #{order_num} has been approved and processed.\n"
                            f"Time: {timestamp}",
                        )
                        logger.info(f"Sent new finance message for order #{order_num}")
                    except Exception as send_error:
                        logger.error(
                            f"Also failed to send new finance message: {send_error}"
                        )

                # Store order in confirmed orders collection for tracking and delivery bots
                try:
                    from src.firebase_db import set_data
                    confirmed_order_data = {
                        **order,
                        "confirmed_at": approved_at,
                        "status": "CONFIRMED",
                        "delivery_status": "pending_assignment"
                    }
                    set_data(f"confirmed_orders/{display_order_number}", confirmed_order_data)
                    logger.info(f"Stored order #{display_order_number} in confirmed_orders collection")

                    # Send initial order tracking notification (single message system)
                    try:
                        from src.bots.order_track_bot import send_order_status_update
                        send_order_status_update(
                            display_order_number,
                            "Payment Approved",
                            "Order has been confirmed and payment verified. Broadcasting to delivery personnel...",
                            replace_previous=False  # Initial message, create new tracking message
                        )
                        logger.info(f"Sent initial order tracking notification for order #{display_order_number}")
                    except Exception as track_error:
                        logger.error(f"Failed to send initial order tracking notification: {track_error}")

                    # Broadcast order to available delivery personnel (no auto-assignment)
                    logger.info(f"🚀 STARTING DELIVERY BROADCAST for order #{display_order_number}")
                    try:
                        from src.utils.delivery_personnel_utils import find_available_personnel
                        from src.config import DELIVERY_BOT_TOKEN
                        import telebot

                        logger.info(f"📡 Initializing delivery bot with token: {DELIVERY_BOT_TOKEN[:20]}...")
                        delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)

                        # Get delivery area ID from order
                        delivery_area_id = order.get('restaurant_area_id') or order.get('area_id')

                        # Debug logging for order data
                        logger.info(f"Order #{display_order_number} data for delivery broadcast:")
                        logger.info(f"  restaurant_area_id: {order.get('restaurant_area_id')}")
                        logger.info(f"  area_id: {order.get('area_id')}")
                        logger.info(f"  delivery_area_id: {delivery_area_id}")

                        if not delivery_area_id:
                            logger.warning(f"No delivery area ID found for order #{display_order_number}, cannot broadcast to delivery personnel")
                            logger.info(f"Available order keys: {list(order.keys())}")
                        else:
                            # Find available delivery personnel with less than 5 orders
                            available_personnel_ids = find_available_personnel(str(delivery_area_id))
                            logger.info(f"Found {len(available_personnel_ids)} available personnel for area {delivery_area_id}: {available_personnel_ids}")

                            if not available_personnel_ids:
                                logger.warning(f"❌ No available delivery personnel for order #{display_order_number} in area {delivery_area_id}")
                                logger.info(f"🔍 DEBUG: Checking all delivery personnel availability...")

                                # Debug: Check all personnel
                                from src.data_storage import load_delivery_personnel_data, load_delivery_personnel_availability_data
                                all_personnel = load_delivery_personnel_data()
                                all_availability = load_delivery_personnel_availability_data()

                                for pid, pdata in all_personnel.items():
                                    availability = all_availability.get(pid, 'unknown')
                                    logger.info(f"  Personnel {pid}: {pdata.get('name')} - Status: {pdata.get('status')} - Availability: {availability} - Areas: {pdata.get('service_areas')}")
                            else:
                                logger.info(f"✅ Broadcasting order #{display_order_number} to {len(available_personnel_ids)} delivery personnel: {available_personnel_ids}")

                                # Get comprehensive order details
                                restaurant = get_restaurant_by_id(order.get("restaurant_id", ""))
                                restaurant_name = restaurant.get("name", "Unknown") if restaurant else "Unknown"

                                # Format order items
                                items_text = ""
                                if "items" in order and order["items"]:
                                    items_text = "\n📋 **Order Items:**\n"
                                    for item in order["items"]:
                                        item_name = item.get("name", "Unknown Item")
                                        item_price = item.get("price", 0)
                                        item_quantity = item.get("quantity", 1)
                                        items_text += f"• {item_name} x{item_quantity} - {item_price * item_quantity} Birr\n"

                                delivery_message = f"""
🚚 **NEW ORDER AVAILABLE**

📋 **Order #{display_order_number}**
🏪 **Restaurant**: {restaurant_name}
📍 **Restaurant Area**: {order.get('area_name', 'N/A')}

👤 **Customer Details:**
📱 **Phone**: {order.get('phone_number', 'N/A')}
👤 **Name**: {order.get('delivery_name', 'N/A')}
📍 **Delivery Address**: {order.get('delivery_location', 'N/A')}
🚪 **Gate**: {order.get('delivery_gate', 'N/A')}
{items_text}
💰 **Subtotal**: {order.get('subtotal', 0)} Birr
🚚 **Delivery Fee**: {order.get('delivery_fee', 0)} Birr
💵 **Total**: {order.get('subtotal', 0) + order.get('delivery_fee', 0)} Birr

⏰ **Order Time**: {order.get('created_at', approved_at)}
✅ **Confirmed**: {approved_at}

⚡ **First to accept gets the order!**
"""

                                # Create accept/decline buttons
                                markup = types.InlineKeyboardMarkup()
                                markup.row(
                                    types.InlineKeyboardButton(
                                        "✅ Accept Order",
                                        callback_data=f"accept_order_{display_order_number}"
                                    ),
                                    types.InlineKeyboardButton(
                                        "❌ Decline",
                                        callback_data=f"decline_order_{display_order_number}"
                                    )
                                )

                                # Get delivery personnel data to find telegram IDs
                                from src.utils.delivery_personnel_utils import delivery_personnel

                                # Broadcast to all available delivery personnel
                                broadcast_count = 0
                                failed_personnel = []

                                # Store message IDs for later cleanup when order is accepted
                                broadcast_message_ids = {}

                                for personnel_id in available_personnel_ids:
                                    try:
                                        personnel_data = delivery_personnel.get(personnel_id)
                                        logger.info(f"🔍 Personnel {personnel_id} data: {personnel_data}")

                                        if personnel_data and personnel_data.get('telegram_id'):
                                            telegram_id = personnel_data['telegram_id']
                                            logger.info(f"📤 Sending message to Telegram ID: {telegram_id}")

                                            # Validate telegram_id is a valid integer
                                            if not isinstance(telegram_id, (int, str)) or not str(telegram_id).isdigit():
                                                logger.warning(f"⚠️  Invalid Telegram ID for personnel {personnel_id}: {telegram_id}")
                                                failed_personnel.append((personnel_id, "Invalid Telegram ID"))
                                                continue

                                            # Send message and store message ID for cleanup
                                            sent_message = delivery_bot.send_message(
                                                int(telegram_id),
                                                delivery_message,
                                                reply_markup=markup,
                                                parse_mode='Markdown'
                                            )

                                            # Store message ID for this personnel
                                            broadcast_message_ids[personnel_id] = {
                                                'telegram_id': int(telegram_id),
                                                'message_id': sent_message.message_id
                                            }

                                            broadcast_count += 1
                                            logger.info(f"✅ Successfully broadcasted order #{display_order_number} to delivery personnel {personnel_id} (Telegram: {telegram_id}, Message ID: {sent_message.message_id})")
                                        else:
                                            logger.warning(f"❌ No telegram ID found for delivery personnel {personnel_id}")
                                            failed_personnel.append((personnel_id, "No Telegram ID"))

                                    except Exception as broadcast_error:
                                        # Enhanced error handling for different types of Telegram API errors
                                        error_message = str(broadcast_error).lower()
                                        personnel_telegram_id = personnel_data.get('telegram_id', 'Unknown') if personnel_data else 'Unknown'

                                        if "chat not found" in error_message:
                                            logger.warning(f"⚠️  Personnel {personnel_id} (Telegram: {personnel_telegram_id}) - Chat not found. User hasn't started conversation with delivery bot.")
                                            failed_personnel.append((personnel_id, "Chat not found"))
                                        elif "forbidden" in error_message or "blocked" in error_message:
                                            logger.warning(f"⚠️  Personnel {personnel_id} (Telegram: {personnel_telegram_id}) - Bot blocked by user.")
                                            failed_personnel.append((personnel_id, "Bot blocked"))
                                        elif "user not found" in error_message:
                                            logger.warning(f"⚠️  Personnel {personnel_id} (Telegram: {personnel_telegram_id}) - Invalid Telegram user ID.")
                                            failed_personnel.append((personnel_id, "Invalid user ID"))
                                        else:
                                            logger.error(f"❌ Failed to broadcast to delivery personnel {personnel_id} (Telegram: {personnel_telegram_id}): {broadcast_error}")
                                            failed_personnel.append((personnel_id, f"API Error: {broadcast_error}"))

                                # Store broadcast message IDs in Firebase for cleanup when order is accepted
                                if broadcast_message_ids:
                                    try:
                                        set_data(f"order_broadcast_messages/{display_order_number}", broadcast_message_ids)
                                        logger.info(f"💾 Stored {len(broadcast_message_ids)} broadcast message IDs for order #{display_order_number}")
                                    except Exception as store_error:
                                        logger.error(f"❌ Failed to store broadcast message IDs for order #{display_order_number}: {store_error}")

                                # Log summary of broadcast results
                                if broadcast_count > 0:
                                    logger.info(f"✅ Successfully broadcasted order #{display_order_number} to {broadcast_count} available delivery personnel")
                                else:
                                    logger.warning(f"⚠️  Failed to broadcast order #{display_order_number} to any delivery personnel")

                                # Log failed personnel for debugging
                                if failed_personnel:
                                    logger.warning(f"📋 Failed delivery personnel for order #{display_order_number}:")
                                    for pid, reason in failed_personnel:
                                        logger.warning(f"   - {pid}: {reason}")

                                    # Consider marking unreachable personnel as inactive
                                    unreachable_count = len([f for f in failed_personnel if f[1] in ["Chat not found", "Bot blocked", "Invalid user ID"]])
                                    if unreachable_count > 0:
                                        logger.info(f"💡 Consider reviewing {unreachable_count} unreachable delivery personnel for potential deactivation")

                    except Exception as delivery_error:
                        logger.error(f"❌ CRITICAL: Failed to broadcast order to delivery personnel: {delivery_error}")
                        import traceback
                        logger.error(f"Full traceback: {traceback.format_exc()}")

                except Exception as e:
                    logger.error(f"Failed to store order #{display_order_number} in confirmed_orders: {e}")

                # Clean up order data
                try:
                    clean_up_order_data(user_id, display_order_number)
                    logger.info(
                        f"Successfully cleaned up order data for order #{display_order_number}"
                    )
                except Exception as cleanup_error:
                    logger.error(f"Error during order cleanup: {cleanup_error}")
                    # Continue execution even if cleanup fails

                # Log final success message
                logger.info(
                    f"Successfully completed approval process for order #{display_order_number}"
                )

            except Exception as e:
                logger.error(f"Error processing approval: {e}")
                finance_bot.send_message(
                    call.message.chat.id,
                    f"❌ Error processing approval for order #{order_number}. Please try again.",
                )

        elif action == "reject":  # Reject
            # Directly prompt for rejection reason without showing intermediate button
            msg = finance_bot.send_message(
                call.message.chat.id,
                f"Please enter your reason for rejecting Order #{order_number}:",
            )

            # Register the next step handler to capture the rejection reason
            finance_bot.register_next_step_handler(
                msg,
                lambda m: process_payment_rejection_with_remarks(
                    m, user_id, order_number
                ),
            )
        else:
            finance_bot.send_message(call.message.chat.id, "❌ Invalid action.")

    except Exception as e:
        logger.error(f"Error in handle_finance_verification: {e}")
        try:
            finance_bot.send_message(
                call.message.chat.id,
                "An error occurred while processing the verification. Please try again.",
            )
        except:
            pass


def process_payment_rejection_with_remarks(message, user_id, order_number):
    """Process payment rejection with remarks"""
    try:
        remarks = message.text
        if not remarks or len(remarks.strip()) == 0:
            remarks = "Payment verification failed"
            logger.warning(
                f"Empty rejection reason for order #{order_number}, using default"
            )

        # First check for the order in the user's orders
        order = orders.get(user_id)

        # If not found in memory, try to get it from Firebase
        if not order:
            try:
                # Try to fetch the order directly from Firebase
                from src.firebase_db import get_current_orders

                firebase_orders = get_current_orders(str(user_id))
                if (
                    firebase_orders
                    and str(user_id) in firebase_orders
                    and firebase_orders[str(user_id)]
                ):
                    order = firebase_orders[str(user_id)]
                    # Update the in-memory orders dictionary
                    orders[user_id] = order
                    logger.info(
                        f"Retrieved order for user {user_id} from Firebase for rejection"
                    )
            except Exception as e:
                logger.error(f"Error retrieving order from Firebase for rejection: {e}")

        # Check pending_admin_reviews if not found in current_orders
        if not order and order_number in pending_admin_reviews:
            try:
                # Use the data from pending_admin_reviews
                order_data = pending_admin_reviews[order_number]
                # Create a minimal order object with required fields
                order = {
                    "status": "PENDING",
                    "order_number": order_number,
                    "delivery_gate": order_data.get("delivery_gate", "Unknown"),
                    "phone_number": order_data.get("phone_number", "Unknown"),
                    "delivery_name": order_data.get("delivery_name", "Customer"),
                    "points_used": order_data.get("points_used", 0),
                }
                # Store it in the in-memory dictionary for future reference
                orders[user_id] = order
                logger.info(
                    f"Retrieved order {order_number} from pending_admin_reviews for rejection"
                )
            except Exception as e:
                logger.error(
                    f"Error retrieving order from pending_admin_reviews for rejection: {e}"
                )

        # Create a minimal order if we still don't have one
        if not order:
            order = {
                "status": "PENDING",
                "order_number": order_number,
                "delivery_gate": "Unknown",
                "phone_number": "Unknown",
                "delivery_name": "Customer",
                "points_used": 0,
            }
            logger.warning(
                f"Created minimal order object for rejection of order #{order_number}"
            )

        # Process the rejection
        try:
            # Check if points were used
            points_used = order.get("points_used", 0) if order else 0

            # Restore points if they were used
            if points_used > 0:
                try:
                    # Use update_points_balance instead of direct in-memory update
                    update_points_balance(user_id, points_used)
                    logger.info(
                        f"Restored {points_used} points to user {user_id} due to payment rejection"
                    )
                except Exception as points_error:
                    logger.error(
                        f"Failed to restore points for user {user_id}: {points_error}"
                    )

            # Send rejection message to user with remarks
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
                types.KeyboardButton("💫 My Points"),
                types.KeyboardButton("ℹ️ Help"),
            )

            try:
                bot.send_message(
                    user_id,
                    f"❌ We couldn't verify your payment for this order.\n\n"
                    f"🔍 Reason: {remarks}\n\n"
                    "Please check your details and try again. We're here to help!\n"
                    f"📞 Need assistance? Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
                    reply_markup=markup,
                )
                logger.info(
                    f"Sent rejection notification to user {user_id} for order #{order_number}"
                )

                # Additional message to guide the user
                bot.send_message(
                    user_id,
                    "You can start a new order from the main menu whenever you're ready. Thank you for your patience! 😊",
                )
            except Exception as e:
                logger.error(
                    f"Failed to send rejection notification to user {user_id}: {e}"
                )
                # Continue with admin notifications even if we fail to notify the user

            # Get user details for the notification
            delivery_name = (
                order.get("delivery_name", "Unknown") if order else "Unknown"
            )
            phone_number = order.get("phone_number", "Unknown") if order else "Unknown"

            # Get current timestamp
            rejection_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Update the original message caption
            try:
                # Check if the message is a photo (with caption) or text message
                if hasattr(message, "message_id") and message.message_id > 0:
                    # Get the previous message (the one with the receipt)
                    prev_message_id = message.message_id - 1

                    # Check if previous message has media
                    try:
                        # Try to update caption first (for photo messages)
                        finance_bot.edit_message_caption(
                            chat_id=message.chat.id,
                            message_id=prev_message_id,
                            caption=f"❌ Payment for Order #{order_number} has been rejected.\n"
                            f"Reason: {remarks}\n"
                            f"Time: {rejection_time}",
                        )
                        logger.info(
                            f"Updated finance message caption for rejected order #{order_number}"
                        )
                    except Exception as caption_error:
                        if (
                            "there is no caption in the message to edit"
                            in str(caption_error).lower()
                        ):
                            # If the message doesn't have a caption, try to edit the text instead
                            try:
                                finance_bot.edit_message_text(
                                    chat_id=message.chat.id,
                                    message_id=prev_message_id,
                                    text=f"❌ Payment for Order #{order_number} has been rejected.\n"
                                    f"Reason: {remarks}\n"
                                    f"Time: {rejection_time}",
                                )
                                logger.info(
                                    f"Updated finance message text for rejected order #{order_number}"
                                )
                            except Exception as text_error:
                                logger.error(
                                    f"Could not update finance message text: {text_error}"
                                )
                                # Fall back to sending a new message
                                _send_new_rejection_message(
                                    order_number, remarks, rejection_time
                                )
                        else:
                            logger.error(
                                f"Could not update finance message caption: {caption_error}"
                            )
                            # Fall back to sending a new message
                            _send_new_rejection_message(
                                order_number, remarks, rejection_time
                            )
                else:
                    # Fall back to sending a new message if message_id is not valid
                    _send_new_rejection_message(order_number, remarks, rejection_time)
            except Exception as e:
                logger.error(f"Could not update finance message: {e}")
                # Send a new message if updating fails
                _send_new_rejection_message(order_number, remarks, rejection_time)

            # Helper function to send a new rejection message
            def _send_new_rejection_message(order_num, reject_remarks, reject_time):
                try:
                    finance_bot.send_message(
                        FINANCE_CHAT_ID,
                        f"❌ Payment for Order #{order_num} has been rejected.\n"
                        f"Reason: {reject_remarks}\n"
                        f"Time: {reject_time}",
                    )
                    logger.info(
                        f"Sent new finance message for rejected order #{order_num}"
                    )
                except Exception as send_error:
                    logger.error(
                        f"Also failed to send new finance message: {send_error}"
                    )

            # Confirm to the finance team that the rejection was processed
            try:
                finance_bot.send_message(
                    message.chat.id,
                    f"✅ Order #{order_number} has been rejected successfully.",
                )
            except Exception as confirm_error:
                logger.error(
                    f"Failed to send confirmation to finance team: {confirm_error}"
                )

            # Clean up order data
            try:
                clean_up_order_data(user_id, order_number)
                logger.info(
                    f"Successfully cleaned up rejected order data for order #{order_number}"
                )
            except Exception as cleanup_error:
                logger.error(f"Error during rejected order cleanup: {cleanup_error}")

            # Final success log
            logger.info(
                f"Successfully completed rejection process for order #{order_number}"
            )

        except Exception as process_error:
            logger.error(f"Error in rejection process: {process_error}")
            try:
                finance_bot.send_message(
                    message.chat.id,
                    f"⚠️ There were some issues processing the rejection, but order #{order_number} has been marked as rejected.",
                )
            except:
                pass

    except Exception as e:
        logger.error(f"Error in process_payment_rejection_with_remarks: {e}")
        try:
            finance_bot.send_message(
                message.chat.id,
                f"❌ Error processing rejection for Order #{order_number}",
            )
        except:
            pass


@register_handler("finance", commands=["start"])
def finance_start(message):
    """Handle the /start command for finance bot"""
    try:
        # Check if the user is authorized
        if str(message.from_user.id) != FINANCE_CHAT_ID:
            finance_bot.reply_to(message, "⚠️ You are not authorized to use this bot.")
            return

        welcome_text = (
            "🔐 Welcome to the Finance Control Panel!\n\n"
            "You will receive payment receipts here and can:\n"
            "• Verify payments\n"
            "• Reject invalid receipts\n"
            "• Monitor transactions\n\n"
            "📝 Commands:\n"
            "/start - Show this message\n"
            "/help - Show finance commands\n"
        )

        finance_bot.reply_to(message, welcome_text)

    except Exception as e:
        logger.error(f"Error in finance_start: {str(e)}")


# Create dedicated handlers for BOA and Awash bank options
@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_PAYMENT_METHOD"
    and any(
        term in message.text.lower()
        for term in ["boa", "abyssinia", "bank of abyssinia"]
    )
)
def handle_boa_option(message):
    """Direct handler for any BOA Bank-related text"""
    user_id = message.from_user.id
    logger.info(
        f"BOA BANK DIRECT HANDLER: Processing text '{message.text}' for user {user_id}"
    )

    # Verify order exists
    order = orders.get(user_id)
    if not order:
        logger.error(
            f"No active order found for user {user_id} when selecting BOA Bank"
        )
        bot.send_message(message.chat.id, "❌ No active order found.")
        bot.send_message(
            message.chat.id,
            "Please start a new order.",
            reply_markup=get_main_menu_markup(),
        )
        return

    # Create a new message with the exact expected text
    new_message = types.Message(
        message_id=message.message_id,
        from_user=message.from_user,
        date=message.date,
        chat=message.chat,
        content_type="text",
        options={},
        json_string=None,
    )
    new_message.text = "🏦 BOA Bank"

    # Process through the main handler
    logger.info(
        f"Redirecting to handle_payment_choice with BOA Bank for user {user_id}"
    )
    handle_payment_choice(new_message)


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_PAYMENT_METHOD"
    and "awash" in message.text.lower()
)
def handle_awash_option(message):
    """Direct handler for any Awash Bank-related text"""
    user_id = message.from_user.id
    logger.info(
        f"AWASH BANK DIRECT HANDLER: Processing text '{message.text}' for user {user_id}"
    )

    # Verify order exists
    order = orders.get(user_id)
    if not order:
        logger.error(
            f"No active order found for user {user_id} when selecting Awash Bank"
        )
        bot.send_message(message.chat.id, "❌ No active order found.")
        bot.send_message(
            message.chat.id,
            "Please start a new order.",
            reply_markup=get_main_menu_markup(),
        )
        return

    # Create a new message with the exact expected text
    new_message = types.Message(
        message_id=message.message_id,
        from_user=message.from_user,
        date=message.date,
        chat=message.chat,
        content_type="text",
        options={},
        json_string=None,
    )
    new_message.text = "🏦 Awash Bank"

    # Process through the main handler
    logger.info(
        f"Redirecting to handle_payment_choice with Awash Bank for user {user_id}"
    )
    handle_payment_choice(new_message)


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_PAYMENT_METHOD"
    and not any(
        button_text in message.text
        for button_text in [
            "🏦 BOA Bank",
            "🏦 Awash Bank",
            "🏦 Zemen Bank",
            "✅ Use Points",
            "❌ Cancel Order",
        ]
    )
    and not any(
        keyword in message.text.lower()
        for keyword in [
            "boa",
            "abyssinia",
            "bank of abyssinia",
            "awash",
            "zemen",
        ]
    )
)
def handle_unknown_payment_method(message):
    """Handler for unknown payment method inputs"""
    user_id = message.from_user.id
    logger.info(f"UNKNOWN PAYMENT METHOD: User {user_id} entered '{message.text}'")

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("📱 Telebirr"),
        types.KeyboardButton("🏦 CBE Bank"),
    )
    markup.add(types.KeyboardButton("🏦 BOA Bank"))
    markup.add(types.KeyboardButton("❌ Cancel Order"))

    bot.send_message(
        message.chat.id,
        "⚠️ Sorry. Currently, we only support Telebirr, CBE Bank, and BOA Bank payments. Please select one of the options below:",
        reply_markup=markup,
    )

    # Keep the status the same to allow selection of a supported payment method
    order_status[user_id] = "AWAITING_PAYMENT_METHOD"


def register_handlers():
    """Register all payment handlers expliclty to ensure proper order"""
    logger.info("Registering payment handlers")
    # Register in order of specificity (most specific first)
    handlers = [
        # 1. Specific keyword handlers
        {
            "handler": handle_boa_option,
            "func": lambda message: order_status.get(message.from_user.id)
            == "AWAITING_PAYMENT_METHOD"
            and any(
                term in message.text.lower()
                for term in ["boa", "abyssinia", "bank of abyssinia"]
            ),
        },
        {
            "handler": handle_awash_option,
            "func": lambda message: order_status.get(message.from_user.id)
            == "AWAITING_PAYMENT_METHOD"
            and "awash" in message.text.lower(),
        },
        # 2. Exact button match handlers
        {
            "handler": handle_payment_choice,
            "func": lambda message: order_status.get(message.from_user.id)
            == "AWAITING_PAYMENT_METHOD"
            and message.text
            in ["📱 Telebirr", "🏦 CBE Bank", "🏦 BOA Bank", "✅ Use Points"],
        },
        {
            "handler": handle_points_confirmation,
            "func": lambda message: order_status.get(message.from_user.id)
            == "AWAITING_POINTS_CONFIRMATION"
            and message.text in ["✅ Confirm Use Points", "❌ Cancel Points Use"],
        },
        {
            "handler": retry_payment_instructions,
            "func": lambda message: message.text == "🔄 Back to Bank Options",
        },
        # 3. Catch-all for unknown payment methods (lowest priority)
        {
            "handler": handle_unknown_payment_method,
            "func": lambda message: order_status.get(message.from_user.id)
            == "AWAITING_PAYMENT_METHOD"
            and not any(
                button_text in message.text
                for button_text in [
                    "🏦 BOA Bank",
                    "🏦 Awash Bank",
                    "✅ Use Points",
                    "❌ Cancel Order",
                ]
            )
            and not any(
                keyword in message.text.lower()
                for keyword in [
                    "boa",
                    "abyssinia",
                    "bank of abyssinia",
                    "awash",
                ]
            ),
        },
    ]

    # Register all handlers in the specified order
    for i, handler_info in enumerate(handlers):
        logger.info(f"Registering handler #{i+1}: {handler_info['handler'].__name__}")
        bot.register_message_handler(
            handler_info["handler"],
            func=handler_info["func"],
            pass_bot=True,
        )

    # Log explicit debug information to confirm handlers are registered
    payment_handlers = [
        handle_boa_option,
        handle_awash_option,
        handle_payment_choice,
        handle_points_confirmation,
        retry_payment_instructions,
        handle_unknown_payment_method,
    ]
    logger.info(f"Registered {len(payment_handlers)} payment handlers")

    # Add direct button handlers as a fallback
    add_direct_button_handlers()


def add_direct_button_handlers():
    """Add direct button handlers as a fallback mechanism"""
    logger.info("Adding direct button handlers for payment options")

    @bot.message_handler(func=lambda message: message.text == "📱 Telebirr")
    def direct_telebirr_handler(message):
        """Direct handler for Telebirr button"""
        user_id = message.from_user.id
        logger.info(f"DIRECT TELEBIRR HANDLER: User {user_id} clicked Telebirr button")

        # Check if user has an active order
        order = orders.get(user_id)
        if not order:
            logger.error(
                f"No active order found for user {user_id} when selecting Telebirr directly"
            )
            bot.send_message(message.chat.id, "❌ No active order found.")
            return

        # Set order status explicitly
        order_status[user_id] = "AWAITING_PAYMENT_METHOD"
        logger.info(
            f"Status set to AWAITING_PAYMENT_METHOD for user {user_id} in direct handler"
        )

        # Process the payment directly instead of calling handle_payment_choice
        logger.info(f"Processing Telebirr payment directly for user {user_id}")

        # Set payment method
        payment_method = "Telebirr"
        order["payment_method"] = payment_method

        # Calculate amount
        subtotal = order.get("subtotal", 0)
        delivery_fee = order.get("delivery_fee", 0)
        total_amount = subtotal + delivery_fee
        points_used = order.get("points_used", 0)
        payment_amount = subtotal if points_used > 0 else total_amount

        # Show payment instructions
        markup = get_payment_receipt_markup()
        payment_text = (
            "📱 *TELEBIRR MOBILE MONEY TRANSFER DETAILS*\n\n"
            f"💰 *Amount to Pay:* {payment_amount} birr\n\n"
            "📱 *Account Details:*\n"
            "• Mobile Money: Telebirr\n"
            f"• Phone Number: {TELEBIRR_PHONE}\n"
            f"• Account Name: {TELEBIRR_NAME}\n\n"
            "📸 *After Payment:*\n"
            "• Take a screenshot of your payment confirmation\n"
            "• Send ONLY a PHOTO/SCREENSHOT (not document or other file type)\n"
            "• Make sure the image is compressed and clear\n\n"
            "Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep!"
        )

        try:
            bot.send_message(
                message.chat.id,
                payment_text,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            logger.info(
                f"Successfully sent Telebirr payment instructions to user {user_id}"
            )

            # Update order status
            order_status[user_id] = "AWAITING_RECEIPT"

            # Store receipt info
            awaiting_receipt[user_id] = {
                "amount": payment_amount,
                "method": payment_method,
                "order_number": order.get("order_number"),
            }

            logger.info(
                f"Successfully processed Telebirr payment selection for user {user_id}"
            )
        except Exception as e:
            logger.error(
                f"Error sending Telebirr payment instructions to user {user_id}: {e}"
            )
            bot.send_message(
                message.chat.id,
                "❌ Error processing payment. Please try again or contact support.\n\n"
                f"📞 Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            )

    @bot.message_handler(func=lambda message: message.text == "🏦 CBE Bank")
    def direct_cbe_handler(message):
        """Direct handler for CBE Bank button"""
        user_id = message.from_user.id
        logger.info(f"DIRECT CBE HANDLER: User {user_id} clicked CBE Bank button")

        # Check if user has an active order
        order = orders.get(user_id)
        if not order:
            logger.error(
                f"No active order found for user {user_id} when selecting CBE Bank directly"
            )
            bot.send_message(message.chat.id, "❌ No active order found.")
            return

        # Set order status explicitly
        order_status[user_id] = "AWAITING_PAYMENT_METHOD"
        logger.info(
            f"Status set to AWAITING_PAYMENT_METHOD for user {user_id} in direct handler"
        )

        # Process the payment directly instead of calling handle_payment_choice
        logger.info(f"Processing CBE Bank payment directly for user {user_id}")

        # Set payment method
        payment_method = "CBE Bank"
        order["payment_method"] = payment_method

        # Calculate amount
        subtotal = order.get("subtotal", 0)
        delivery_fee = order.get("delivery_fee", 0)
        total_amount = subtotal + delivery_fee
        points_used = order.get("points_used", 0)
        payment_amount = subtotal if points_used > 0 else total_amount

        # Show payment instructions
        markup = get_payment_receipt_markup()
        payment_text = (
            "🏦 *CBE BANK TRANSFER DETAILS*\n\n"
            f"💰 *Amount to Pay:* {payment_amount} birr\n\n"
            "🏦 *Account Details:*\n"
            "• Bank: Commercial Bank of Ethiopia (CBE)\n"
            f"• Account Number: {CBE_ACCOUNT_NUMBER}\n"
            f"• Account Name: {CBE_ACCOUNT_NAME}\n\n"
            "📸 *After Payment:*\n"
            "• Take a screenshot of your payment confirmation\n"
            "• Send ONLY a PHOTO/SCREENSHOT (not document or other file type)\n"
            "• Make sure the image is compressed and clear\n\n"
            "Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep!"
        )

        try:
            bot.send_message(
                message.chat.id,
                payment_text,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            logger.info(f"Successfully sent CBE payment instructions to user {user_id}")

            # Update order status
            order_status[user_id] = "AWAITING_RECEIPT"

            # Store receipt info
            awaiting_receipt[user_id] = {
                "amount": payment_amount,
                "method": payment_method,
                "order_number": order.get("order_number"),
            }

            logger.info(
                f"Successfully processed CBE Bank payment selection for user {user_id}"
            )
        except Exception as e:
            logger.error(
                f"Error sending CBE payment instructions to user {user_id}: {e}"
            )
            bot.send_message(
                message.chat.id,
                "❌ Error processing payment. Please try again or contact support.\n\n"
                f"📞 Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            )

    @bot.message_handler(func=lambda message: message.text == "🏦 BOA Bank")
    def direct_boa_handler(message):
        """Direct handler for BOA Bank button"""
        user_id = message.from_user.id
        logger.info(f"DIRECT BOA HANDLER: User {user_id} clicked BOA Bank button")

        # Check if user has an active order
        order = orders.get(user_id)
        if not order:
            logger.error(
                f"No active order found for user {user_id} when selecting BOA Bank directly"
            )
            bot.send_message(message.chat.id, "❌ No active order found.")
            return

        # Set order status explicitly
        order_status[user_id] = "AWAITING_PAYMENT_METHOD"
        logger.info(
            f"Status set to AWAITING_PAYMENT_METHOD for user {user_id} in direct handler"
        )

        # Process the payment directly instead of calling handle_payment_choice
        logger.info(f"Processing BOA Bank payment directly for user {user_id}")

        # Set payment method
        payment_method = "BOA Bank"
        order["payment_method"] = payment_method

        # Calculate amount
        subtotal = order.get("subtotal", 0)
        delivery_fee = order.get("delivery_fee", 0)
        total_amount = subtotal + delivery_fee
        points_used = order.get("points_used", 0)
        payment_amount = subtotal if points_used > 0 else total_amount

        # Show payment instructions
        markup = get_payment_receipt_markup()
        payment_text = (
            "🏦 *BOA BANK TRANSFER DETAILS*\n\n"
            f"💰 *Amount to Pay:* {payment_amount} birr\n\n"
            "🏦 *Account Details:*\n"
            "• Bank: Bank of Abissinya (BOA)\n"
            f"• Account Number: {BOA_ACCOUNT_NUMBER}\n"
            f"• Account Name: {BOA_ACCOUNT_NAME}\n\n"
            "📸 *After Payment:*\n"
            "• Take a screenshot of your payment confirmation\n"
            "• Send ONLY a PHOTO/SCREENSHOT (not document or other file type)\n"
            "• Make sure the image is compressed and clear\n\n"
            "Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep!"
        )

        try:
            bot.send_message(
                message.chat.id,
                payment_text,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            logger.info(f"Successfully sent BOA payment instructions to user {user_id}")

            # Update order status
            order_status[user_id] = "AWAITING_RECEIPT"

            # Store receipt info
            awaiting_receipt[user_id] = {
                "amount": payment_amount,
                "method": payment_method,
                "order_number": order.get("order_number"),
            }

            logger.info(
                f"Successfully processed BOA Bank payment selection for user {user_id}"
            )
        except Exception as e:
            logger.error(
                f"Error sending BOA payment instructions to user {user_id}: {e}"
            )
            bot.send_message(
                message.chat.id,
                "❌ Error processing payment. Please try again or contact support.\n\n"
                f"📞 Contact us: {SUPPORT_PHONE_1}, {SUPPORT_PHONE_2} or {SUPPORT_TELEGRAM}",
            )

    logger.info("Direct button handlers added successfully")
