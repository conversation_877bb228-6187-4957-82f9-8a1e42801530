#!/usr/bin/env python3
"""
<PERSON>ript to set up delivery personnel for testing the broadcast system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import set_data, get_data
from src.utils.delivery_personnel_utils import create_delivery_personnel, verify_delivery_personnel
from src.data_models import delivery_personnel, delivery_personnel_availability, delivery_personnel_capacity

def setup_delivery_personnel():
    """Set up delivery personnel for testing"""
    print("=== SETTING UP DELIVERY PERSONNEL ===")
    
    # Check if personnel already exist
    existing_personnel = get_data("delivery_personnel") or {}
    print(f"Found {len(existing_personnel)} existing personnel")
    
    # Personnel to create/update
    personnel_configs = [
        {
            "name": "Admin User",
            "phone_number": "+251963630623",
            "telegram_id": "7729984017",
            "email": "<EMAIL>",
            "service_areas": ["1", "2", "3", "4", "5"],
            "vehicle_type": "motorcycle",
            "max_capacity": 5
        },
        {
            "name": "Test Delivery Person",
            "phone_number": "+251912345678",
            "telegram_id": "5546595738",
            "email": "<EMAIL>",
            "service_areas": ["1", "2", "3"],
            "vehicle_type": "motorcycle",
            "max_capacity": 5
        }
    ]
    
    created_personnel = []
    
    for config in personnel_configs:
        # Check if this telegram_id already exists
        existing_id = None
        for pid, pdata in existing_personnel.items():
            if pdata.get('telegram_id') == config['telegram_id']:
                existing_id = pid
                break
        
        if existing_id:
            print(f"Personnel with Telegram ID {config['telegram_id']} already exists: {existing_id}")
            personnel_id = existing_id
        else:
            # Create new personnel
            personnel_id = create_delivery_personnel(
                name=config['name'],
                phone_number=config['phone_number'],
                service_areas=config['service_areas'],
                telegram_id=config['telegram_id'],
                email=config['email'],
                vehicle_type=config['vehicle_type'],
                max_capacity=config['max_capacity']
            )
            print(f"Created new personnel: {personnel_id}")
        
        if personnel_id:
            # Verify the personnel
            if verify_delivery_personnel(personnel_id, verified=True):
                print(f"✅ Verified personnel {personnel_id}")
            
            # Set status to available
            set_data(f"delivery_personnel_availability/{personnel_id}", "available")
            set_data(f"delivery_personnel_capacity/{personnel_id}", 0)
            
            # Update personnel status in their record
            personnel_data = get_data(f"delivery_personnel/{personnel_id}")
            if personnel_data:
                personnel_data['status'] = 'available'
                personnel_data['is_verified'] = True
                set_data(f"delivery_personnel/{personnel_id}", personnel_data)
                print(f"✅ Set {personnel_id} to available status")
            
            created_personnel.append(personnel_id)
    
    print(f"\n=== SETUP COMPLETE ===")
    print(f"Personnel ready for delivery: {len(created_personnel)}")
    
    # Verify setup
    print("\n=== VERIFICATION ===")
    for personnel_id in created_personnel:
        personnel_data = get_data(f"delivery_personnel/{personnel_id}")
        availability = get_data(f"delivery_personnel_availability/{personnel_id}")
        capacity = get_data(f"delivery_personnel_capacity/{personnel_id}")
        
        print(f"Personnel {personnel_id}:")
        print(f"  Name: {personnel_data.get('name')}")
        print(f"  Telegram ID: {personnel_data.get('telegram_id')}")
        print(f"  Status: {personnel_data.get('status')}")
        print(f"  Availability: {availability}")
        print(f"  Verified: {personnel_data.get('is_verified')}")
        print(f"  Capacity: {capacity}/{personnel_data.get('max_capacity')}")
        print(f"  Service Areas: {personnel_data.get('service_areas')}")
        print()

if __name__ == "__main__":
    setup_delivery_personnel()
