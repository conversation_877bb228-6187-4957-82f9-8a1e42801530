"""
Utility functions for delivery personnel management.
Contains core business logic for personnel assignment, capacity management, and optimization.
"""

import datetime
import uuid
from typing import Dict, List, Optional, Tuple, Any

from src.config import logger
from src.data_models import (
    DeliveryPersonnel,
    DeliveryAssignment,
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    areas_data,
)
from src.data_storage import (
    save_delivery_personnel,
    save_delivery_personnel_assignments,
    save_delivery_personnel_availability,
    save_delivery_personnel_capacity,
    save_delivery_personnel_zones,
    save_delivery_personnel_performance,
)


def create_delivery_personnel(
    name: str,
    phone_number: str,
    service_areas: List[str],
    telegram_id: Optional[str] = None,
    email: Optional[str] = None,
    vehicle_type: str = "motorcycle",
    max_capacity: int = 5
) -> str:
    """
    Create a new delivery personnel record.
    
    Args:
        name: Full name of the delivery person
        phone_number: Phone number with country code
        service_areas: List of area IDs they can serve
        telegram_id: Optional Telegram user ID
        email: Optional email address
        vehicle_type: Type of vehicle (motorcycle, bicycle, car, walking)
        max_capacity: Maximum concurrent orders (default: 5)
    
    Returns:
        personnel_id: Unique identifier for the personnel
    """
    try:
        # Generate unique personnel ID
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"
        
        # Create personnel object
        personnel = DeliveryPersonnel(personnel_id)
        personnel.name = name
        personnel.phone_number = phone_number
        personnel.telegram_id = telegram_id
        personnel.email = email
        personnel.service_areas = service_areas
        personnel.vehicle_type = vehicle_type
        personnel.max_capacity = max_capacity
        personnel.status = "offline"  # Start as offline until verified
        personnel.is_verified = False  # Requires admin verification
        
        # Save to global data structures
        delivery_personnel[personnel_id] = personnel.to_dict()
        delivery_personnel_availability[personnel_id] = "offline"
        delivery_personnel_capacity[personnel_id] = 0
        delivery_personnel_zones[personnel_id] = service_areas
        delivery_personnel_performance[personnel_id] = {
            "total_deliveries": 0,
            "successful_deliveries": 0,
            "average_rating": 5.0,
            "total_distance": 0.0,
            "average_delivery_time": 0.0,
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Save to Firebase
        save_delivery_personnel()
        save_delivery_personnel_availability()
        save_delivery_personnel_capacity()
        save_delivery_personnel_zones()
        save_delivery_personnel_performance()
        
        logger.info(f"Created new delivery personnel: {name} (ID: {personnel_id})")
        return personnel_id
        
    except Exception as e:
        logger.error(f"Error creating delivery personnel: {e}")
        raise


def verify_delivery_personnel(personnel_id: str, verified: bool = True) -> bool:
    """
    Verify or unverify a delivery personnel.
    
    Args:
        personnel_id: ID of the personnel to verify
        verified: True to verify, False to unverify
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if personnel_id not in delivery_personnel:
            logger.error(f"Personnel not found: {personnel_id}")
            return False
        
        # Update personnel record
        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        personnel.is_verified = verified
        
        # If verifying, set status to available
        if verified:
            personnel.status = "available"
            delivery_personnel_availability[personnel_id] = "available"
        else:
            personnel.status = "offline"
            delivery_personnel_availability[personnel_id] = "offline"
        
        # Save updated data
        delivery_personnel[personnel_id] = personnel.to_dict()
        save_delivery_personnel()
        save_delivery_personnel_availability()
        
        logger.info(f"Personnel {personnel_id} verification set to: {verified}")
        return True
        
    except Exception as e:
        logger.error(f"Error verifying personnel {personnel_id}: {e}")
        return False


def find_available_personnel(area_id: str, exclude_personnel: List[str] = None) -> List[str]:
    """
    Find available delivery personnel for a specific area.

    Args:
        area_id: ID of the delivery area
        exclude_personnel: List of personnel IDs to exclude from search

    Returns:
        List of available personnel IDs, sorted by capacity and rating
    """
    try:
        if exclude_personnel is None:
            exclude_personnel = []

        available_personnel = []

        # Debug logging
        logger.info(f"🔍 Finding available personnel for area {area_id}")
        logger.info(f"📊 Total personnel in system: {len(delivery_personnel)}")
        logger.info(f"📊 Availability data: {len(delivery_personnel_availability)} records")
        logger.info(f"📊 Capacity data: {len(delivery_personnel_capacity)} records")
        logger.info(f"📊 Zones data: {len(delivery_personnel_zones)} records")

        for personnel_id, personnel_data in delivery_personnel.items():
            # Skip excluded personnel
            if personnel_id in exclude_personnel:
                logger.info(f"⏭️  Skipping excluded personnel {personnel_id}")
                continue

            personnel = DeliveryPersonnel.from_dict(personnel_data)

            # Get live capacity data (not from personnel object)
            current_capacity = delivery_personnel_capacity.get(personnel_id, 0)
            availability_status = delivery_personnel_availability.get(personnel_id, "unknown")

            # Update personnel object with live capacity data
            personnel.current_capacity = current_capacity

            # Debug logging for each personnel
            logger.info(f"👤 Checking personnel {personnel_id} ({personnel.name}):")
            logger.info(f"   Status: {personnel.status}")
            logger.info(f"   Verified: {personnel.is_verified}")
            logger.info(f"   Capacity: {current_capacity}/{personnel.max_capacity}")
            logger.info(f"   Availability: {availability_status}")
            logger.info(f"   Service Areas: {personnel.service_areas}")
            logger.info(f"   Can serve area {area_id}: {personnel.can_serve_area(area_id)}")
            logger.info(f"   is_available(): {personnel.is_available()}")

            # Check if personnel is available and can serve the area
            if (personnel.is_available() and
                personnel.can_serve_area(area_id) and
                availability_status == "available"):

                logger.info(f"✅ Personnel {personnel_id} is available for area {area_id}")
                available_personnel.append({
                    "personnel_id": personnel_id,
                    "current_capacity": current_capacity,
                    "max_capacity": personnel.max_capacity,
                    "rating": personnel.rating,
                    "total_deliveries": personnel.total_deliveries
                })
            else:
                logger.info(f"❌ Personnel {personnel_id} is NOT available for area {area_id}")
                if not personnel.is_available():
                    logger.info(f"   Reason: is_available() = False")
                if not personnel.can_serve_area(area_id):
                    logger.info(f"   Reason: cannot serve area {area_id}")
                if availability_status != "available":
                    logger.info(f"   Reason: availability status = {availability_status}")

        # Sort by capacity (ascending) then by rating (descending)
        available_personnel.sort(
            key=lambda x: (x["current_capacity"], -x["rating"], -x["total_deliveries"])
        )

        result = [p["personnel_id"] for p in available_personnel]
        logger.info(f"🎯 Found {len(result)} available personnel for area {area_id}: {result}")

        return result

    except Exception as e:
        logger.error(f"Error finding available personnel for area {area_id}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return []


def assign_order_to_personnel(order_number: str, personnel_id: str, delivery_fee: float = 0.0) -> Optional[str]:
    """
    Assign an order to delivery personnel.
    
    Args:
        order_number: Order number to assign
        personnel_id: ID of the personnel to assign to
        delivery_fee: Delivery fee for this order
    
    Returns:
        assignment_id if successful, None otherwise
    """
    try:
        # Validate personnel exists and is available
        if personnel_id not in delivery_personnel:
            logger.error(f"Personnel not found: {personnel_id}")
            return None
        
        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        
        if not personnel.is_available():
            logger.error(f"Personnel {personnel_id} is not available")
            return None
        
        # Enhanced capacity check with real-time validation
        current_capacity = get_real_time_capacity(personnel_id)
        if current_capacity >= personnel.max_capacity:
            logger.error(f"Personnel {personnel_id} is at maximum capacity ({current_capacity}/{personnel.max_capacity})")
            return None

        # Generate assignment ID
        assignment_id = f"assign_{uuid.uuid4().hex[:8]}"

        # Create assignment with enhanced tracking
        assignment = DeliveryAssignment(assignment_id, order_number, personnel_id)
        assignment.delivery_fee = delivery_fee
        assignment.estimated_pickup_time = datetime.datetime.now() + datetime.timedelta(minutes=15)
        assignment.estimated_delivery_time = datetime.datetime.now() + datetime.timedelta(minutes=45)
        assignment.status = "assigned"

        # Update capacity with validation
        new_capacity = increment_personnel_capacity(personnel_id)
        if new_capacity is None:
            logger.error(f"Failed to increment capacity for personnel {personnel_id}")
            return None
        
        # Save assignment
        delivery_personnel_assignments[assignment_id] = assignment.to_dict()
        
        # Save all updated data
        save_delivery_personnel()
        save_delivery_personnel_assignments()
        save_delivery_personnel_capacity()
        save_delivery_personnel_availability()

        # Notify order tracking bot about the assignment
        try:
            from src.bots.order_track_bot import notify_delivery_assignment
            notify_delivery_assignment(order_number, personnel.name, personnel.phone_number)
        except Exception as e:
            logger.error(f"Failed to send assignment notification: {e}")

        logger.info(f"Assigned order {order_number} to personnel {personnel_id} (Assignment: {assignment_id}, Capacity: {new_capacity}/{personnel.max_capacity})")
        return assignment_id
        
    except Exception as e:
        logger.error(f"Error assigning order {order_number} to personnel {personnel_id}: {e}")
        return None


def complete_delivery_assignment(assignment_id: str, success: bool = True, customer_rating: Optional[int] = None) -> bool:
    """
    Complete a delivery assignment and update personnel metrics.
    
    Args:
        assignment_id: ID of the assignment to complete
        success: Whether the delivery was successful
        customer_rating: Customer rating (1-5)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if assignment_id not in delivery_personnel_assignments:
            logger.error(f"Assignment not found: {assignment_id}")
            return False
        
        assignment_data = delivery_personnel_assignments[assignment_id]
        assignment = DeliveryAssignment.from_dict(assignment_data)
        personnel_id = assignment.personnel_id
        
        # Update assignment
        assignment.status = "delivered" if success else "cancelled"
        assignment.actual_delivery_time = datetime.datetime.now()
        if customer_rating:
            assignment.customer_rating = customer_rating
        
        # Update personnel capacity with enhanced tracking
        new_capacity = decrement_personnel_capacity(personnel_id)
        if new_capacity is None:
            logger.warning(f"Failed to decrement capacity for personnel {personnel_id}")
            # Continue with completion even if capacity update fails
            new_capacity = delivery_personnel_capacity.get(personnel_id, 0)
        
        # Update performance metrics
        if personnel_id in delivery_personnel:
            personnel_data = delivery_personnel[personnel_id]
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            
            personnel.total_deliveries += 1
            if success:
                personnel.successful_deliveries += 1
            
            # Update rating if provided
            if customer_rating:
                # Simple moving average for rating
                total_ratings = personnel.total_deliveries
                current_total = personnel.rating * (total_ratings - 1)
                personnel.rating = (current_total + customer_rating) / total_ratings
            
            delivery_personnel[personnel_id] = personnel.to_dict()
            
            # Update performance data
            performance = delivery_personnel_performance.get(personnel_id, {})
            performance.update({
                "total_deliveries": personnel.total_deliveries,
                "successful_deliveries": personnel.successful_deliveries,
                "average_rating": personnel.rating,
                "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            delivery_personnel_performance[personnel_id] = performance
        
        # Save assignment
        delivery_personnel_assignments[assignment_id] = assignment.to_dict()
        
        # Save all updated data
        save_delivery_personnel()
        save_delivery_personnel_assignments()
        save_delivery_personnel_capacity()
        save_delivery_personnel_availability()
        save_delivery_personnel_performance()
        
        logger.info(f"Completed assignment {assignment_id} - Success: {success}")
        return True
        
    except Exception as e:
        logger.error(f"Error completing assignment {assignment_id}: {e}")
        return False


# ===== AVAILABILITY STATUS MANAGEMENT FUNCTIONS =====

def set_personnel_status(personnel_id: str, new_status: str, reason: str = "") -> bool:
    """
    Set the availability status of a delivery personnel.

    Args:
        personnel_id: ID of the delivery personnel
        new_status: New status (available, busy, offline)
        reason: Optional reason for status change

    Returns:
        True if status was updated successfully, False otherwise
    """
    try:
        if personnel_id not in delivery_personnel:
            logger.error(f"Personnel {personnel_id} not found")
            return False

        valid_statuses = ["available", "busy", "offline"]
        if new_status not in valid_statuses:
            logger.error(f"Invalid status: {new_status}. Must be one of {valid_statuses}")
            return False

        # Get current personnel data
        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        old_status = personnel.status

        # Update status
        personnel.status = new_status
        delivery_personnel_availability[personnel_id] = new_status
        delivery_personnel[personnel_id] = personnel.to_dict()

        # Save to Firebase
        save_delivery_personnel()
        save_delivery_personnel_availability()

        # Log status change
        reason_text = f" (Reason: {reason})" if reason else ""
        logger.info(f"Personnel {personnel_id} status changed from {old_status} to {new_status}{reason_text}")

        return True

    except Exception as e:
        logger.error(f"Error setting personnel status: {e}")
        return False


def toggle_personnel_status(personnel_id: str) -> Optional[str]:
    """
    Toggle personnel status between available and offline.

    Args:
        personnel_id: ID of the delivery personnel

    Returns:
        New status if successful, None if failed
    """
    try:
        if personnel_id not in delivery_personnel:
            logger.error(f"Personnel {personnel_id} not found")
            return None

        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        current_status = personnel.status

        # Determine new status
        if current_status == "offline":
            new_status = "available"
        elif current_status in ["available", "busy"]:
            new_status = "offline"
        else:
            new_status = "available"  # Default fallback

        # Set new status
        if set_personnel_status(personnel_id, new_status, "Manual toggle"):
            return new_status
        else:
            return None

    except Exception as e:
        logger.error(f"Error toggling personnel status: {e}")
        return None


def get_personnel_status(personnel_id: str) -> Optional[str]:
    """
    Get the current availability status of a delivery personnel.

    Args:
        personnel_id: ID of the delivery personnel

    Returns:
        Current status or None if personnel not found
    """
    try:
        if personnel_id not in delivery_personnel:
            return None

        # Check both sources for consistency
        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        availability_status = delivery_personnel_availability.get(personnel_id, personnel.status)

        # Use availability data as primary source
        return availability_status

    except Exception as e:
        logger.error(f"Error getting personnel status: {e}")
        return None


def update_status_based_on_capacity(personnel_id: str) -> bool:
    """
    Automatically update personnel status based on current capacity.

    Args:
        personnel_id: ID of the delivery personnel

    Returns:
        True if status was updated, False otherwise
    """
    try:
        if personnel_id not in delivery_personnel:
            return False

        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        current_capacity = get_real_time_capacity(personnel_id)
        current_status = personnel.status

        # Don't change status if personnel is offline
        if current_status == "offline":
            return False

        # Determine appropriate status based on capacity
        if current_capacity >= personnel.max_capacity:
            new_status = "busy"
        else:
            new_status = "available"

        # Update status if it has changed
        if current_status != new_status:
            return set_personnel_status(personnel_id, new_status, "Automatic capacity-based update")

        return True

    except Exception as e:
        logger.error(f"Error updating status based on capacity: {e}")
        return False


def get_personnel_by_status(status: str) -> List[Dict]:
    """
    Get all personnel with a specific status.

    Args:
        status: Status to filter by (available, busy, offline)

    Returns:
        List of personnel data dictionaries
    """
    try:
        personnel_list = []

        for personnel_id, personnel_data in delivery_personnel.items():
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            current_status = delivery_personnel_availability.get(personnel_id, personnel.status)

            if current_status == status:
                personnel_info = {
                    "personnel_id": personnel_id,
                    "name": personnel.name,
                    "phone_number": personnel.phone_number,
                    "status": current_status,
                    "current_capacity": get_real_time_capacity(personnel_id),
                    "max_capacity": personnel.max_capacity,
                    "service_areas": personnel.service_areas,
                    "is_verified": personnel.is_verified
                }
                personnel_list.append(personnel_info)

        return personnel_list

    except Exception as e:
        logger.error(f"Error getting personnel by status: {e}")
        return []


def get_status_summary() -> Dict[str, any]:
    """
    Get comprehensive status summary for all personnel.

    Returns:
        Dictionary with status counts and details
    """
    try:
        summary = {
            "total_personnel": len(delivery_personnel),
            "status_counts": {
                "available": 0,
                "busy": 0,
                "offline": 0
            },
            "verified_counts": {
                "verified": 0,
                "unverified": 0
            },
            "personnel_by_status": {
                "available": [],
                "busy": [],
                "offline": []
            },
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        for personnel_id, personnel_data in delivery_personnel.items():
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            current_status = delivery_personnel_availability.get(personnel_id, personnel.status)

            # Update counts
            summary["status_counts"][current_status] += 1
            if personnel.is_verified:
                summary["verified_counts"]["verified"] += 1
            else:
                summary["verified_counts"]["unverified"] += 1

            # Add to status-specific lists
            personnel_info = {
                "personnel_id": personnel_id,
                "name": personnel.name,
                "current_capacity": get_real_time_capacity(personnel_id),
                "max_capacity": personnel.max_capacity,
                "is_verified": personnel.is_verified
            }
            summary["personnel_by_status"][current_status].append(personnel_info)

        return summary

    except Exception as e:
        logger.error(f"Error getting status summary: {e}")
        return {
            "total_personnel": 0,
            "status_counts": {"available": 0, "busy": 0, "offline": 0},
            "verified_counts": {"verified": 0, "unverified": 0},
            "personnel_by_status": {"available": [], "busy": [], "offline": []},
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }


def validate_all_statuses() -> Dict[str, any]:
    """
    Validate and synchronize all personnel statuses with their actual capacity.

    Returns:
        Dictionary with validation results and corrections made
    """
    try:
        validation_results = {
            "total_checked": 0,
            "corrections_made": 0,
            "status_mismatches": [],
            "corrected_personnel": [],
            "validation_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        for personnel_id, personnel_data in delivery_personnel.items():
            validation_results["total_checked"] += 1
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            current_capacity = get_real_time_capacity(personnel_id)
            current_status = personnel.status
            availability_status = delivery_personnel_availability.get(personnel_id, current_status)

            # Skip offline personnel
            if current_status == "offline":
                continue

            # Determine correct status based on capacity
            correct_status = "busy" if current_capacity >= personnel.max_capacity else "available"

            # Check for mismatches
            if current_status != correct_status or availability_status != correct_status:
                validation_results["status_mismatches"].append({
                    "personnel_id": personnel_id,
                    "name": personnel.name,
                    "current_status": current_status,
                    "availability_status": availability_status,
                    "correct_status": correct_status,
                    "current_capacity": current_capacity,
                    "max_capacity": personnel.max_capacity
                })

                # Correct the status
                if set_personnel_status(personnel_id, correct_status, "Automatic validation correction"):
                    validation_results["corrections_made"] += 1
                    validation_results["corrected_personnel"].append({
                        "personnel_id": personnel_id,
                        "name": personnel.name,
                        "old_status": current_status,
                        "new_status": correct_status
                    })

        return validation_results

    except Exception as e:
        logger.error(f"Error validating statuses: {e}")
        return {
            "total_checked": 0,
            "corrections_made": 0,
            "status_mismatches": [],
            "corrected_personnel": [],
            "validation_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "error": str(e)
        }


def get_personnel_summary() -> Dict[str, int]:
    """
    Get a summary of personnel status.
    
    Returns:
        Dictionary with counts of personnel by status
    """
    try:
        summary = {
            "total": 0,
            "available": 0,
            "busy": 0,
            "offline": 0,
            "verified": 0,
            "unverified": 0
        }
        
        for personnel_id, personnel_data in delivery_personnel.items():
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            summary["total"] += 1
            
            if personnel.is_verified:
                summary["verified"] += 1
            else:
                summary["unverified"] += 1
            
            status = delivery_personnel_availability.get(personnel_id, personnel.status)
            if status == "available":
                summary["available"] += 1
            elif status == "busy":
                summary["busy"] += 1
            else:
                summary["offline"] += 1
        
        return summary
        
    except Exception as e:
        logger.error(f"Error getting personnel summary: {e}")
        return {"total": 0, "available": 0, "busy": 0, "offline": 0, "verified": 0, "unverified": 0}


def auto_assign_order(order_number: str, delivery_area_id: str, delivery_fee: float = 0.0) -> Optional[str]:
    """
    Automatically assign an order to the best available delivery personnel.
    
    Args:
        order_number: Order number to assign
        delivery_area_id: ID of the delivery area
        delivery_fee: Delivery fee for this order
    
    Returns:
        assignment_id if successful, None if no personnel available
    """
    try:
        # Find available personnel for the area
        available_personnel = find_available_personnel(delivery_area_id)
        
        if not available_personnel:
            logger.warning(f"No available personnel for area {delivery_area_id}")
            return None
        
        # Try to assign to the first available personnel (already sorted by capacity and rating)
        for personnel_id in available_personnel:
            assignment_id = assign_order_to_personnel(order_number, personnel_id, delivery_fee)
            if assignment_id:
                logger.info(f"Auto-assigned order {order_number} to personnel {personnel_id}")
                return assignment_id
        
        logger.warning(f"Failed to auto-assign order {order_number} to any personnel")
        return None

    except Exception as e:
        logger.error(f"Error auto-assigning order {order_number}: {e}")
        return None


# Enhanced Capacity Tracking Functions

def get_real_time_capacity(personnel_id: str) -> int:
    """
    Get real-time capacity by counting active assignments.

    Args:
        personnel_id: ID of the personnel

    Returns:
        Current number of active orders
    """
    try:
        if personnel_id not in delivery_personnel:
            return 0

        # Count active assignments (not completed or cancelled)
        active_count = 0
        for assignment_data in delivery_personnel_assignments.values():
            if (assignment_data.get("personnel_id") == personnel_id and
                assignment_data.get("status") in ["assigned", "accepted", "picked_up"]):
                active_count += 1

        # Update stored capacity if different
        stored_capacity = delivery_personnel_capacity.get(personnel_id, 0)
        if stored_capacity != active_count:
            delivery_personnel_capacity[personnel_id] = active_count
            logger.info(f"Updated capacity for {personnel_id}: {stored_capacity} -> {active_count}")
            save_delivery_personnel_capacity()

        return active_count

    except Exception as e:
        logger.error(f"Error getting real-time capacity for {personnel_id}: {e}")
        return delivery_personnel_capacity.get(personnel_id, 0)


def increment_personnel_capacity(personnel_id: str) -> Optional[int]:
    """
    Safely increment personnel capacity with validation.

    Args:
        personnel_id: ID of the personnel

    Returns:
        New capacity if successful, None if failed
    """
    try:
        if personnel_id not in delivery_personnel:
            logger.error(f"Personnel {personnel_id} not found")
            return None

        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)

        current_capacity = get_real_time_capacity(personnel_id)

        if current_capacity >= personnel.max_capacity:
            logger.warning(f"Cannot increment capacity for {personnel_id}: at max ({current_capacity}/{personnel.max_capacity})")
            return None

        new_capacity = current_capacity + 1
        delivery_personnel_capacity[personnel_id] = new_capacity

        # Update personnel status if at max capacity using centralized status management
        if new_capacity >= personnel.max_capacity:
            set_personnel_status(personnel_id, "busy", f"Reached max capacity ({new_capacity}/{personnel.max_capacity})")

        return new_capacity

    except Exception as e:
        logger.error(f"Error incrementing capacity for {personnel_id}: {e}")
        return None


def decrement_personnel_capacity(personnel_id: str) -> Optional[int]:
    """
    Safely decrement personnel capacity with validation.

    Args:
        personnel_id: ID of the personnel

    Returns:
        New capacity if successful, None if failed
    """
    try:
        if personnel_id not in delivery_personnel:
            logger.error(f"Personnel {personnel_id} not found")
            return None

        personnel_data = delivery_personnel[personnel_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)

        current_capacity = get_real_time_capacity(personnel_id)

        if current_capacity <= 0:
            logger.warning(f"Cannot decrement capacity for {personnel_id}: already at 0")
            return 0

        new_capacity = current_capacity - 1
        delivery_personnel_capacity[personnel_id] = new_capacity

        # Update personnel status if no longer at max capacity using centralized status management
        if new_capacity < personnel.max_capacity and personnel.status == "busy":
            set_personnel_status(personnel_id, "available", f"Capacity reduced to {new_capacity}/{personnel.max_capacity}")

        return new_capacity

    except Exception as e:
        logger.error(f"Error decrementing capacity for {personnel_id}: {e}")
        return None


def validate_all_capacities() -> Dict[str, Dict[str, int]]:
    """
    Validate and sync all personnel capacities with actual assignments.

    Returns:
        Dictionary with validation results for each personnel
    """
    validation_results = {}

    try:
        for personnel_id in delivery_personnel.keys():
            stored_capacity = delivery_personnel_capacity.get(personnel_id, 0)
            real_capacity = get_real_time_capacity(personnel_id)

            validation_results[personnel_id] = {
                "stored": stored_capacity,
                "actual": real_capacity,
                "synced": stored_capacity == real_capacity
            }

            if stored_capacity != real_capacity:
                logger.warning(f"Capacity mismatch for {personnel_id}: stored={stored_capacity}, actual={real_capacity}")

        logger.info(f"Capacity validation completed for {len(validation_results)} personnel")
        return validation_results

    except Exception as e:
        logger.error(f"Error validating capacities: {e}")
        return {}


def get_capacity_summary() -> Dict[str, any]:
    """
    Get comprehensive capacity summary for all personnel.

    Returns:
        Summary statistics and personnel capacity details
    """
    try:
        summary = {
            "total_personnel": len(delivery_personnel),
            "available_personnel": 0,
            "busy_personnel": 0,
            "offline_personnel": 0,
            "total_capacity": 0,
            "used_capacity": 0,
            "available_capacity": 0,
            "personnel_details": []
        }

        for personnel_id, personnel_data in delivery_personnel.items():
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            current_capacity = get_real_time_capacity(personnel_id)
            available_capacity = max(0, personnel.max_capacity - current_capacity)

            # Update summary counters
            summary["total_capacity"] += personnel.max_capacity
            summary["used_capacity"] += current_capacity
            summary["available_capacity"] += available_capacity

            if personnel.status == "available":
                summary["available_personnel"] += 1
            elif personnel.status == "busy":
                summary["busy_personnel"] += 1
            else:
                summary["offline_personnel"] += 1

            # Add personnel details
            summary["personnel_details"].append({
                "personnel_id": personnel_id,
                "name": personnel.name,
                "status": personnel.status,
                "current_capacity": current_capacity,
                "max_capacity": personnel.max_capacity,
                "available_capacity": available_capacity,
                "utilization_rate": (current_capacity / personnel.max_capacity * 100) if personnel.max_capacity > 0 else 0
            })

        # Calculate overall utilization
        summary["overall_utilization"] = (summary["used_capacity"] / summary["total_capacity"] * 100) if summary["total_capacity"] > 0 else 0

        return summary

    except Exception as e:
        logger.error(f"Error generating capacity summary: {e}")
        return {}


def monitor_capacity_alerts() -> List[Dict[str, any]]:
    """
    Monitor for capacity-related alerts and warnings.

    Returns:
        List of alerts requiring attention
    """
    alerts = []

    try:
        for personnel_id, personnel_data in delivery_personnel.items():
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            current_capacity = get_real_time_capacity(personnel_id)

            # High capacity warning (80% or more)
            if current_capacity >= (personnel.max_capacity * 0.8):
                alerts.append({
                    "type": "high_capacity",
                    "personnel_id": personnel_id,
                    "name": personnel.name,
                    "current_capacity": current_capacity,
                    "max_capacity": personnel.max_capacity,
                    "message": f"{personnel.name} is at {current_capacity}/{personnel.max_capacity} capacity (80%+ utilization)"
                })

            # Capacity mismatch alert
            stored_capacity = delivery_personnel_capacity.get(personnel_id, 0)
            if stored_capacity != current_capacity:
                alerts.append({
                    "type": "capacity_mismatch",
                    "personnel_id": personnel_id,
                    "name": personnel.name,
                    "stored_capacity": stored_capacity,
                    "actual_capacity": current_capacity,
                    "message": f"{personnel.name} has capacity mismatch: stored={stored_capacity}, actual={current_capacity}"
                })

        return alerts

    except Exception as e:
        logger.error(f"Error monitoring capacity alerts: {e}")
        return []


# ============================================================================
# DELIVERY ASSIGNMENT FUNCTIONS
# ============================================================================

def get_delivery_assignment_by_order(order_number: str) -> Optional[Dict[str, Any]]:
    """
    Get delivery assignment for a specific order.

    Args:
        order_number: The order number to look up

    Returns:
        Assignment data if found, None otherwise
    """
    try:
        from src.data_storage import load_delivery_personnel_assignments
        assignments_data = load_delivery_personnel_assignments()

        # Search through all assignments to find the one for this order
        for assignment_id, assignment in assignments_data.items():
            if assignment.get('order_number') == order_number:
                return assignment

        return None

    except Exception as e:
        logger.error(f"Error getting delivery assignment for order {order_number}: {str(e)}")
        return None


def get_assignments_for_personnel(personnel_id: str) -> List[Dict[str, Any]]:
    """
    Get all assignments for a specific delivery personnel.

    Args:
        personnel_id: The personnel ID to look up

    Returns:
        List of assignment data
    """
    try:
        from src.data_storage import load_delivery_personnel_assignments
        assignments_data = load_delivery_personnel_assignments()

        personnel_assignments = []
        for assignment_id, assignment in assignments_data.items():
            if assignment.get('personnel_id') == personnel_id:
                personnel_assignments.append(assignment)

        return personnel_assignments

    except Exception as e:
        logger.error(f"Error getting assignments for personnel {personnel_id}: {str(e)}")
        return []


def update_assignment_status(order_number: str, personnel_id: str, new_status: str) -> bool:
    """
    Update the status of a delivery assignment.

    Args:
        order_number: The order number
        personnel_id: The personnel ID (for verification)
        new_status: The new status to set

    Returns:
        True if successful, False otherwise
    """
    try:
        from src.data_storage import load_delivery_personnel_assignments, save_delivery_personnel_assignments
        assignments_data = load_delivery_personnel_assignments()

        # Find the assignment for this order
        assignment_id = None
        for aid, assignment in assignments_data.items():
            if (assignment.get('order_number') == order_number and
                assignment.get('personnel_id') == personnel_id):
                assignment_id = aid
                break

        if not assignment_id:
            logger.warning(f"No assignment found for order {order_number} and personnel {personnel_id}")
            return False

        # Update the status and timestamp
        assignments_data[assignment_id]['status'] = new_status

        # Add timestamp for specific status changes
        import datetime
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if new_status == 'accepted':
            assignments_data[assignment_id]['accepted_at'] = current_time
        elif new_status == 'picked_up':
            assignments_data[assignment_id]['actual_pickup_time'] = current_time
        elif new_status == 'delivered':
            assignments_data[assignment_id]['actual_delivery_time'] = current_time

        # Save the updated assignments
        success = save_delivery_personnel_assignments(assignments_data)

        if success:
            logger.info(f"Updated assignment status for order {order_number}: {new_status}")

            # Update capacity if order is completed or cancelled
            if new_status in ['delivered', 'cancelled']:
                decrement_personnel_capacity(personnel_id)

        return success

    except Exception as e:
        logger.error(f"Error updating assignment status for order {order_number}: {str(e)}")
        return False


def get_delivery_personnel_by_telegram_id(telegram_id: str) -> Optional[Any]:
    """
    Get delivery personnel by their Telegram ID.

    Args:
        telegram_id: The Telegram ID to look up

    Returns:
        DeliveryPersonnel object if found, None otherwise
    """
    try:
        personnel_data = load_delivery_personnel_data()

        for personnel_id, personnel_dict in personnel_data.items():
            if personnel_dict.get('telegram_id') == telegram_id:
                # Convert dict back to DeliveryPersonnel object using from_dict method
                from src.data_models import DeliveryPersonnel
                personnel = DeliveryPersonnel.from_dict(personnel_dict)
                return personnel

        return None

    except Exception as e:
        logger.error(f"Error getting personnel by Telegram ID {telegram_id}: {str(e)}")
        return None


def get_delivery_personnel_by_id(personnel_id: str) -> Optional[Dict[str, Any]]:
    """
    Get delivery personnel data by personnel ID.

    Args:
        personnel_id: The personnel ID to look up

    Returns:
        Personnel data dict if found, None otherwise
    """
    try:
        personnel_data = load_delivery_personnel_data()

        if personnel_id in personnel_data:
            return personnel_data[personnel_id]

        return None

    except Exception as e:
        logger.error(f"Error getting personnel by ID {personnel_id}: {str(e)}")
        return None


def validate_personnel_telegram_access():
    """Validate which delivery personnel can receive Telegram messages"""
    try:
        from src.config import DELIVERY_BOT_TOKEN
        import telebot

        logger.info("🔍 Validating delivery personnel Telegram access...")

        # Initialize delivery bot for testing
        test_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)

        load_delivery_personnel_data()
        accessible_personnel = []
        inaccessible_personnel = []

        for personnel_id, personnel_data in delivery_personnel.items():
            telegram_id = personnel_data.get('telegram_id')

            if not telegram_id:
                inaccessible_personnel.append((personnel_id, "No Telegram ID"))
                continue

            if not isinstance(telegram_id, (int, str)) or not str(telegram_id).isdigit():
                inaccessible_personnel.append((personnel_id, "Invalid Telegram ID format"))
                continue

            try:
                # Try to get chat info (this will fail if chat doesn't exist)
                chat_info = test_bot.get_chat(int(telegram_id))
                accessible_personnel.append((personnel_id, telegram_id, chat_info.first_name or "Unknown"))
                logger.info(f"✅ Personnel {personnel_id} (Telegram: {telegram_id}) is accessible")

            except Exception as e:
                error_message = str(e).lower()
                if "chat not found" in error_message:
                    reason = "Chat not found - user hasn't started conversation"
                elif "forbidden" in error_message:
                    reason = "Bot blocked by user"
                elif "user not found" in error_message:
                    reason = "Invalid Telegram user ID"
                else:
                    reason = f"API Error: {e}"

                inaccessible_personnel.append((personnel_id, reason))
                logger.warning(f"⚠️  Personnel {personnel_id} (Telegram: {telegram_id}) is inaccessible: {reason}")

        # Log summary
        logger.info(f"📊 Telegram Access Validation Results:")
        logger.info(f"   ✅ Accessible: {len(accessible_personnel)} personnel")
        logger.info(f"   ❌ Inaccessible: {len(inaccessible_personnel)} personnel")

        if inaccessible_personnel:
            logger.warning(f"📋 Inaccessible delivery personnel:")
            for pid, reason in inaccessible_personnel:
                logger.warning(f"   - {pid}: {reason}")

        return accessible_personnel, inaccessible_personnel

    except Exception as e:
        logger.error(f"Error validating personnel Telegram access: {e}")
        return [], []
