"""
Configuration settings for the Wiz Aroma Delivery Bot.
Contains all constants, environment variables, and configuration settings.
"""

import os
import ast
import logging
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import our custom logging utility
from src.utils.logging_utils import get_logger

# Get the logger
logger = get_logger()

# Default log level
DEFAULT_LOG_LEVEL = logging.INFO

# Test mode for development (set to True to bypass token validation)
TEST_MODE = False

# Configure timeouts and retries for Telegram API
RETRY_ON_ERROR = True
CONNECT_TIMEOUT = 3
READ_TIMEOUT = 5

# Configure watchdog interval (in seconds)
WATCHDOG_INTERVAL = 30

# Bot Configuration from .env file
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_BOT_TOKEN = os.getenv("ADMIN_BOT_TOKEN")
FINANCE_BOT_TOKEN = os.getenv("FINANCE_BOT_TOKEN")
MAINTENANCE_BOT_TOKEN = os.getenv("MAINTENANCE_BOT_TOKEN")
NOTIFICATION_BOT_TOKEN = os.getenv("NOTIFICATION_BOT_TOKEN")

# New specialized bot tokens
ORDER_TRACK_BOT_TOKEN = "7620861307:AAFjFTuIQzl5X1PQxBXeEVMHPjdgxMRT7Qo"
DELIVERY_BOT_TOKEN = "7540693452:AAFfaPNdqd7jln7ZlLVtss6LBr1tCG84cM8"

# Validate essential environment variables if not in test mode
if not TEST_MODE and not all(
    [BOT_TOKEN, ADMIN_BOT_TOKEN, FINANCE_BOT_TOKEN, NOTIFICATION_BOT_TOKEN]
):
    logger.error("Missing required bot tokens in .env file!")
    logger.error("Please check your .env file and make sure all tokens are set.")
    sys.exit(1)
elif TEST_MODE and not all(
    [BOT_TOKEN, ADMIN_BOT_TOKEN, FINANCE_BOT_TOKEN, NOTIFICATION_BOT_TOKEN]
):
    logger.warning("Running in TEST_MODE with placeholder tokens.")
    # Use placeholder tokens in test mode if not provided
    if not BOT_TOKEN:
        BOT_TOKEN = "test_bot_token"
    if not ADMIN_BOT_TOKEN:
        ADMIN_BOT_TOKEN = "test_admin_bot_token"
    if not FINANCE_BOT_TOKEN:
        FINANCE_BOT_TOKEN = "test_finance_bot_token"
    if not MAINTENANCE_BOT_TOKEN:
        MAINTENANCE_BOT_TOKEN = "test_maintenance_bot_token"
    if not NOTIFICATION_BOT_TOKEN:
        NOTIFICATION_BOT_TOKEN = "test_notification_bot_token"

# Access control for specialized bots
ORDER_TRACK_BOT_AUTHORIZED_IDS = [7729984017]  # Only this Telegram ID can access order tracking bot
DELIVERY_BOT_AUTHORIZED_IDS = [7729984017, 5546595738]  # These Telegram IDs can access delivery bot

# Chat IDs from .env file
try:
    ADMIN_CHAT_IDS = ast.literal_eval(
        os.getenv("ADMIN_CHAT_IDS", "[]")
    )  # Convert string list to actual list
    if not ADMIN_CHAT_IDS:
        logger.warning("No admin chat IDs configured!")
except (SyntaxError, ValueError) as e:
    logger.error(f"Error parsing ADMIN_CHAT_IDS: {e}")
    logger.error("Setting ADMIN_CHAT_IDS to empty list")
    ADMIN_CHAT_IDS = []

FINANCE_CHAT_ID = os.getenv("FINANCE_CHAT_ID")
if not FINANCE_CHAT_ID:
    logger.warning("No finance chat ID configured!")

# Notification bot configuration
NOTIFICATION_CHAT_ID = os.getenv("NOTIFICATION_CHAT_ID")
if not NOTIFICATION_CHAT_ID:
    logger.warning("No notification chat ID configured!")

# Maintenance bot configuration
MAINTENANCE_CHAT_ID = os.getenv("MAINTENANCE_CHAT_ID")
if not MAINTENANCE_CHAT_ID:
    logger.warning(
        "No maintenance chat ID configured. Using the first admin ID if available."
    )
    MAINTENANCE_CHAT_ID = ADMIN_CHAT_IDS[0] if ADMIN_CHAT_IDS else None

# Email Configuration from .env file
EMAIL_ADDRESS = os.getenv("EMAIL_ADDRESS")
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD")

if not EMAIL_ADDRESS or not EMAIL_PASSWORD:
    logger.warning(
        "Email credentials not configured! Email notifications will not work."
    )

# Payment Information from .env file
# Telebirr
TELEBIRR_PHONE = os.getenv("TELEBIRR_PHONE")
TELEBIRR_NAME = os.getenv("TELEBIRR_NAME")

# CBE Bank
CBE_ACCOUNT_NUMBER = os.getenv("CBE_ACCOUNT_NUMBER")
CBE_ACCOUNT_NAME = os.getenv("CBE_ACCOUNT_NAME")

# BOA Bank
BOA_ACCOUNT_NUMBER = os.getenv("BOA_ACCOUNT_NUMBER")
BOA_ACCOUNT_NAME = os.getenv("BOA_ACCOUNT_NAME")


# Contact Information
SUPPORT_PHONE_1 = os.getenv("SUPPORT_PHONE_1")
SUPPORT_PHONE_2 = os.getenv("SUPPORT_PHONE_2")
SUPPORT_TELEGRAM = os.getenv("SUPPORT_TELEGRAM")

# Validate payment information
if not all(
    [
        TELEBIRR_PHONE,
        TELEBIRR_NAME,
        CBE_ACCOUNT_NUMBER,
        CBE_ACCOUNT_NAME,
        BOA_ACCOUNT_NUMBER,
        BOA_ACCOUNT_NAME,
    ]
):
    logger.warning("Some payment information is missing in .env file!")

# Data directory
DATA_DIR = "data_files"

# File paths for persistent storage
POINTS_FILE = os.path.join(DATA_DIR, "user_points.json")
ORDER_HISTORY_FILE = os.path.join(DATA_DIR, "user_order_history.json")
USER_NAMES_FILE = os.path.join(DATA_DIR, "user_names.json")
USER_PHONE_NUMBERS_FILE = os.path.join(DATA_DIR, "user_phone_numbers.json")
USER_EMAILS_FILE = os.path.join(DATA_DIR, "user_emails.json")

# Data storage file paths
AREAS_FILE = os.path.join(DATA_DIR, "areas.json")
RESTAURANTS_FILE = os.path.join(DATA_DIR, "restaurants.json")
MENUS_FILE = os.path.join(DATA_DIR, "menus.json")
DELIVERY_LOCATIONS_FILE = os.path.join(DATA_DIR, "delivery_locations.json")
DELIVERY_FEES_FILE = os.path.join(DATA_DIR, "delivery_fees.json")
FAVORITE_ORDERS_FILE = os.path.join(DATA_DIR, "favorite_orders.json")
CURRENT_ORDERS_FILE = os.path.join(DATA_DIR, "current_orders.json")
ORDER_STATUS_FILE = os.path.join(DATA_DIR, "order_status.json")
PENDING_ADMIN_REVIEWS_FILE = os.path.join(DATA_DIR, "pending_admin_reviews.json")
ADMIN_REMARKS_FILE = os.path.join(DATA_DIR, "admin_remarks.json")
AWAITING_RECEIPT_FILE = os.path.join(DATA_DIR, "awaiting_receipt.json")
DELIVERY_LOCATIONS_TEMP_FILE = os.path.join(DATA_DIR, "delivery_locations_temp.json")
USER_ORDER_COUNTS_FILE = os.path.join(DATA_DIR, "user_order_counts.json")
CURRENT_ORDER_NUMBERS_FILE = os.path.join(DATA_DIR, "current_order_numbers.json")

# Group restaurants by area
restaurants = {
    "Bole Area": {
        4: {"name": "Helen Megeb-bet"},
        1: {"name": "Barech"},
        2: {"name": "Mesi"},
        3: {"name": "Bole Mami"},
        5: {"name": "Melat Erteb(PS bet)"},
        6: {"name": "Fike Burger"},
        7: {"name": "Bonali Burger"},
        8: {"name": "Abenezer Burger"},
        9: {"name": "Marta Megeb-bet"},
    },
    "Geda Gate Area": {
        16: {"name": "Mami-bet"},
        15: {"name": "Selam Erteb"},
        12: {"name": "Hawinet Megeb-bet"},
        13: {"name": "Etu"},
        10: {"name": "Food Case"},
        17: {"name": "Bontu"},
        11: {"name": "Ja Kushena"},
        18: {"name": "Ikmah Erteb (Roll)"},
        14: {"name": "Beza"},
    },
    "Kereyu Area": {
        19: {"name": "Flavor Coffee"},
    },
    "College Mecheresha Area": {
        20: {"name": "Mame Erteb"},
    },
    "Stadium Area": {
        21: {"name": "Melkamu Megeb and Erteb"},
        22: {"name": "Melkamu"},
        23: {"name": "Daniel"},
        24: {"name": "Enat Megeb-bet"},
        25: {"name": "Mery Megeb-bet"},
    },
}

# Update the delivery fees dictionary with all gates
delivery_fees = {
    "Bole Area": {
        "Applied Library": 20,
        "Federal Dorm": 20,
        "Anfi": 30,
        "Central Library": 30,
        "Masters Dorm (Lebs Matebiya)": 35,
        "B-371 (Fresh)": 40,
    },
    "Geda Gate Area": {
        "Central Library": 20,
        "Anfi": 25,
        "Applied Library": 30,
        "Federal Dorm": 35,
        "Masters Dorm (Lebs Matebiya)": 30,
        "B-371 (Fresh)": 35,
    },
    "Kereyu Area": {
        "Applied Library": 25,
        "Federal Dorm": 20,
        "Central Library": 40,
        "Anfi": 40,
        "Masters Dorm (Lebs Matebiya)": 40,
        "B-371 (Fresh)": 45,
    },
    "College Mecheresha Area": {
        "Central Library": 30,
        "Anfi": 35,
        "Applied Library": 40,
        "Federal Dorm": 45,
        "Masters Dorm (Lebs Matebiya)": 30,
        "B-371 (Fresh)": 25,
    },
    "Stadium Area": {
        "Central Library": 25,
        "Anfi": 20,
        "Applied Library": 35,
        "Federal Dorm": 30,
        "Masters Dorm (Lebs Matebiya)": 20,
        "B-371 (Fresh)": 20,
    },
}

# Menus are now loaded dynamically from src.data.menus
